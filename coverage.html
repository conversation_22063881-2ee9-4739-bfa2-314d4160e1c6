
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>refund: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/discharge.go (100.0%)</option>
				
				<option value="file1">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/expired.go (84.6%)</option>
				
				<option value="file2">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/fundback.go (0.0%)</option>
				
				<option value="file3">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/polling.go (0.0%)</option>
				
				<option value="file4">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/precheck.go (0.0%)</option>
				
				<option value="file5">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/reconcile.go (7.3%)</option>
				
				<option value="file6">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/refund.go (1.9%)</option>
				
				<option value="file7">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/retry.go (0.0%)</option>
				
				<option value="file8">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/settle.go (0.0%)</option>
				
				<option value="file9">gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/refund/topup.go (0.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package refund

import (
        "context"
        "time"

        "github.com/avast/retry-go"
        "github.com/pkg/errors"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

func (uc *Usecase) GetEarlyDischargeLogByID(ctx context.Context, paymentID int64) (*model.EarlyDischargeLog, error) <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        earlyDischargeLog, err := uc.paymentRepo.GetEarlyDischargeLogByID(ctx, paymentID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get early discharge log fail", "error", err)
                return nil, err
        }</span>
        <span class="cov8" title="1">if earlyDischargeLog == nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "early discharge log not found")
                return nil, errors.New("early discharge log not found")
        }</span>

        <span class="cov8" title="1">return earlyDischargeLog, nil</span>
}

func (uc *Usecase) TriggerPollingEarlyDischargeStatus(ctx context.Context, paymentID int64, zalopayID int64) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)
        retryOtps := []retry.Option{
                retry.Attempts(3),
                retry.Delay(3 * time.Second),
        }

        err := retry.Do(func() error </span><span class="cov8" title="1">{
                err := uc.taskJob.ExecuteEarlyDischargePollingJob(ctx, &amp;model.EarlyDischargeStatusWorkflowRequest{
                        PaymentID: paymentID,
                        ZalopayID: zalopayID,
                })
                if err != nil </span><span class="cov8" title="1">{
                        return err
                }</span>
                <span class="cov8" title="1">return nil</span>
        }, retryOtps...)
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "trigger polling early discharge status fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">logger.Infow("msg", "[Trigger] polling early discharge status success", "zalopayID", zalopayID, "paymentID", paymentID)

        return nil</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package refund

import (
        "context"
        "sync"
        "time"

        "github.com/avast/retry-go"
        "github.com/pkg/errors"
        "github.com/spf13/cast"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
        workerExpired = 5
)

type ExpiredRefundSettleRequest struct {
        RefundID     int64
        SettleAmount int64
        RefZPTransID int64
}

func (uc *Usecase) ExcuteExpiredRefundSettlement(ctx context.Context, params *ExpiredRefundSettleRequest) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "excute expired refund settlement")

        // Precheck if expired refund settlement is needed
        eligible, err := uc.isEligibleForExpiredRefundSettle(ctx, params.RefundID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "check eligible for expired refund settlement fail", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">if !eligible </span><span class="cov8" title="1">{
                logger.Infow("msg", "refund log is not eligible for expired refund settlement")
                return nil
        }</span>

        <span class="cov8" title="1">account, purchase, bankRoute, err := uc.getResourceForPreparingExpiredSettle(ctx, params.RefZPTransID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get resource for preparing expired settle fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "begin transaction fail", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">defer uc.txn.RollbackTx(tCtx)

        settleOrder, err := uc.buildExpiredRefundSettleOrder(params, purchase)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "build expired refund settle order fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">settleOrder, err = uc.orderRepo.CreateRefundSettleOrder(tCtx, settleOrder)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create refund settle order fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">repayLog := model.NewRepaymentLogForSettlement(settleOrder, account, bankRoute)
        repayLog, err = uc.paymentRepo.CreateRepaymentLog(tCtx, *repayLog)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create repayment log fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit transaction fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">if err := uc.ProcessRepayment(ctx, repayLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "process repayment fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "excute expired refund settlement success", "settle_id", settleOrder.ID)

        return nil</span>
}

func (uc *Usecase) ProcessRepayment(ctx context.Context, repayLog *model.RepaymentLog) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "process repayment")

        repayReq := model.ODRepaymentRequest{
                RefOrderID:         repayLog.Order.ID,
                PartnerReqID:       idgen.GenPaymentTransID(),
                Amount:             repayLog.Order.Amount,
                PaymentDescription: repayLog.Order.Description,
                ZalopayID:          repayLog.AccountInfo.ZalopayID,
                CorpAccountNumber:  repayLog.BankRoute.BankAccountNumber,
                CorpAccountName:    repayLog.BankRoute.BankAccountName,
                BankAccountNumber:  repayLog.AccountInfo.PartnerAccountId,
                BankAccountName:    repayLog.AccountInfo.PartnerAccountName,
        }
        repayRes, err := uc.connector.ODRepayment(ctx, repayReq)
        if err != nil || repayRes.IsError() </span><span class="cov8" title="1">{
                if err == nil </span><span class="cov8" title="1">{
                        err = repayRes.CIMBError
                }</span>
                <span class="cov8" title="1">logger.Errorw("msg", "OD repayment fail", "error", err)
                return uc.handleSubmitRepaymentFailed(ctx, repayLog, err)</span>
        }

        <span class="cov8" title="1">repayLog = repayLog.HandlePartnerRepayResult(repayRes)
        if err = uc.paymentRepo.UpdateRepaymentLogStatus(ctx, *repayLog); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "update repayment log fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">switch repayLog.Status </span>{
        case model.PaymentStatusSucceeded, model.PaymentStatusFailed:<span class="cov8" title="1">
                if err = uc.PublishRefundExpiredResult(ctx, repayLog); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "publish refund expired result fail", "error", err)
                        return err
                }</span>
                <span class="cov8" title="1">logger.Infow("msg", "publish expired refund result success", "repay_log_id", repayLog.ID)
                return nil</span>
        case model.PaymentStatusPending, model.PaymentStatusProcessing:<span class="cov8" title="1">
                if err = uc.TriggerPollingRepayStatus(ctx, repayLog); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "trigger polling repay status fail", "error", err)
                        return err
                }</span>
                <span class="cov8" title="1">logger.Infow("msg", "trigger polling repay status success", "repay_log_id", repayLog.ID)
                return nil</span>
        default:<span class="cov0" title="0">
                logger.Errorw("msg", "invalid repayment log status", "status", repayLog.Status)
                return errors.New("invalid repayment log status")</span>
        }
}

func (uc *Usecase) ProcessExpiredRefund(ctx context.Context) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "start process expired refund")

        settleIDsCh, err := uc.buildListRefundLogExpiredCh(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "build list refund log expired channel fail", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">if settleIDsCh == nil </span><span class="cov0" title="0">{
                logger.Infow("msg", "no expired refund logs to process")
                return nil
        }</span>

        <span class="cov8" title="1">waitGroup := new(sync.WaitGroup)
        waitGroup.Add(workerExpired)

        for i := range workerExpired </span><span class="cov8" title="1">{
                go func(id int) </span><span class="cov8" title="1">{
                        defer waitGroup.Done()
                        logger.Infow("msg", "worker process expired refund logs", "worker_id", id)
                        uc.workerProcessExpired(ctx, settleIDsCh)
                }</span>(i)
        }

        <span class="cov8" title="1">waitGroup.Wait()

        return nil</span>
}

func (uc *Usecase) TriggerPollingRepayStatus(ctx context.Context, repayLog *model.RepaymentLog) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)
        retryOpts := []retry.Option{
                retry.Attempts(3),
                retry.Delay(3 * time.Second),
        }

        logger.Infow("msg", "trigger polling repay status")

        err := retry.Do(func() error </span><span class="cov8" title="1">{
                err := uc.taskJob.ExecuteExpiredRefundRepaymentPollingJob(ctx, &amp;model.RepaymentStatusPollingRequest{
                        OrderID:   repayLog.Order.ID,
                        PaymentID: repayLog.ID,
                })
                if err != nil </span><span class="cov8" title="1">{
                        return err
                }</span>
                <span class="cov8" title="1">return nil</span>
        }, retryOpts...)
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "trigger polling repay status fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">logger.Infow("msg", "trigger polling repay status success", "repay_log_id", repayLog.ID)

        return nil</span>
}

func (uc *Usecase) PublishRefundExpiredResult(ctx context.Context, repayLog *model.RepaymentLog) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)
        retryOpts := []retry.Option{
                retry.Attempts(3),
                retry.Delay(3 * time.Second),
        }

        err := retry.Do(func() error </span><span class="cov8" title="1">{
                order, err := uc.orderRepo.GetRefundSettleOrder(ctx,
                        repayLog.Order.AppTransID,
                        repayLog.Order.AppID,
                )
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorw("msg", "get refund settle order fail", "error", err)
                        return err
                }</span>

                <span class="cov8" title="1">err = uc.settleNotifier.PublishRefundExpiredResult(ctx, order, repayLog)
                if err != nil </span><span class="cov8" title="1">{
                        return err
                }</span>
                <span class="cov8" title="1">return nil</span>
        }, retryOpts...)
        <span class="cov8" title="1">if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "publish refund expired result fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">logger.Infow("msg", "publish expired refund result")

        return nil</span>
}

func (uc *Usecase) workerProcessExpired(ctx context.Context, settleIDsCh &lt;-chan int64) <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        for settleID := range settleIDsCh </span><span class="cov8" title="1">{
                logger.Infow("msg", "process expired refund", "settle_id", settleID)

                // Get refund logs by settle ID
                refundSettle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorw("msg", "get refund logs by settle ID fail", "error", err)
                        continue</span>
                }

                <span class="cov8" title="1">err = uc.processExpiredRefundLogs(ctx, refundSettle)
                if err != nil </span><span class="cov8" title="1">{
                        logger.Errorw("msg", "process expired refund fail", "error", err)
                        continue</span>
                }

                <span class="cov8" title="1">logger.Infow("msg", "process expired refund success", "settle_id", settleID)

                newCtx := context.WithoutCancel(ctx)
                go uc.TriggerJobReconcileRefundSettlements(newCtx, refundSettle.ZPTransID)</span>
        }
}

func (uc *Usecase) processExpiredRefundLogs(ctx context.Context, settle *model.RefundSettle) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "begin transaction fail", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">defer uc.txn.RollbackTx(tCtx)

        refundLogs, err := uc.repo.GetRefundLogsExpiredByZPTransIDForUpdate(tCtx, settle.ZPTransID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get refund logs by ZPTransID fail", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">if len(refundLogs) == 0 </span><span class="cov8" title="1">{
                logger.Infow("msg", "no refund logs to process")
                return nil
        }</span>

        <span class="cov8" title="1">refundLogsCast := model.RefundOrders(refundLogs)

        if err = uc.repo.MarkRefundLogsAsExpiredByIDs(tCtx, refundLogsCast.GetIDs()); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "mark refund logs as expired fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">if err = uc.settleNotifier.PublishRefundExpiredEvents(tCtx, settle.ZPTransID, refundLogsCast); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "publish refund settle result fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "commit transaction fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">logger.Infow("msg", "process expired refund success", "settle_id", settle.ID)

        return nil</span>
}

func (uc *Usecase) buildListRefundLogExpiredCh(ctx context.Context) (&lt;-chan int64, error) <span class="cov8" title="1">{
        lastID := int64(0)
        logger := uc.logger.WithContext(ctx)
        result := make(chan int64, channelSize)

        go func() </span><span class="cov8" title="1">{
                defer close(result)
                for </span><span class="cov8" title="1">{
                        cursor := cast.ToString(lastID)
                        pagination := &amp;model.Pagination{
                                Limit:  inquirySize,
                                Cursor: &amp;cursor,
                        }
                        listRfLogs, err := uc.repo.GetRefundSettleIDsHasExpiredItem(ctx, pagination)
                        if err != nil </span><span class="cov8" title="1">{
                                logger.Errorw("msg", "get list refund log has expired fail", "error", err)
                                return
                        }</span>
                        <span class="cov8" title="1">if len(listRfLogs) == 0 </span><span class="cov8" title="1">{
                                // No more refund log to process
                                logger.Infow("msg", "no more refund log to process")
                                break</span>
                        }
                        <span class="cov8" title="1">for _, item := range listRfLogs </span><span class="cov8" title="1">{
                                result &lt;- item
                        }</span>
                        <span class="cov8" title="1">lastID = listRfLogs[len(listRfLogs)-1]</span>
                }
        }()

        <span class="cov8" title="1">return result, nil</span>
}

func (uc *Usecase) isEligibleForExpiredRefundSettle(ctx context.Context, refundID int64) (bool, error) <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "precheck expired refund settle")

        ctx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "begin transaction fail", "error", err)
                return false, err
        }</span>
        <span class="cov8" title="1">defer uc.txn.RollbackTx(ctx)

        // Check if refund log is expired
        refundLog, err := uc.repo.GetRefundLogForUpdate(ctx, refundID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get refund log fail", "error", err)
                return false, err
        }</span>
        <span class="cov8" title="1">if refundLog.ProcessType != model.RefundProcessTypeRepayment </span><span class="cov8" title="1">{
                logger.Infow("msg", "refund log is not repayment type")
                return false, nil
        }</span>

        <span class="cov8" title="1">settleOrder, err := uc.orderRepo.GetRefundSettleOrderByRefundID(ctx, refundID)
        if errors.Is(err, model.ErrOrderNotFound) </span><span class="cov8" title="1">{
                logger.Infow("msg", "refund settle order not found")
                return true, nil
        }</span>
        <span class="cov8" title="1">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle order fail", "error", err)
                return false, err
        }</span>

        <span class="cov8" title="1">repayLog, err := uc.paymentRepo.GetRepaymentByOrderID(ctx, settleOrder.ID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get repayment log fail", "error", err)
                return false, err
        }</span>

        <span class="cov8" title="1">if err = uc.txn.CommitTx(ctx); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "commit transaction fail", "error", err)
                return false, err
        }</span>

        <span class="cov8" title="1">return settleOrder.IsFailedOrCancelled() &amp;&amp; repayLog.Status.IsFailedStatus(), nil</span>
}

func (uc *Usecase) buildExpiredRefundSettleOrder(params *ExpiredRefundSettleRequest, payment *model.PurchaseOrder) (*model.RefundSettleOrder, error) <span class="cov8" title="1">{
        config, ok := uc.orderCfgs.GetConfigRefundSettle("")
        if !ok </span><span class="cov8" title="1">{
                return nil, errors.New("refund settle order config not found")
        }</span>

        <span class="cov0" title="0">acctInfo := payment.AccountInfo
        result := model.NewRefundSettleOrder(
                acctInfo.ZalopayID,
                acctInfo.PartnerCode,
                params.RefZPTransID,
                params.SettleAmount,
        )
        result.AppID = config.GetAppId()
        result.RefundID = params.RefundID
        result.AppTransID = idgen.GenAppTransID()
        result.Description = SettleOrderDescription

        return result, nil</span>
}

func (uc *Usecase) getResourceForPreparingExpiredSettle(ctx context.Context, refZPTransID int64) (*model.Account, *model.PurchaseOrder, *model.BankRoute, error) <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        purchase, err := uc.paymentRepo.GetPaymentByTransID(ctx, refZPTransID)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get purchase fail", "error", err)
                return nil, nil, nil, err
        }</span>

        <span class="cov8" title="1">account, err := uc.accountAdapter.GetAccount(ctx,
                purchase.AccountInfo.ZalopayID,
                purchase.AccountInfo.PartnerCode,
        )
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get account fail", "error", err)
                return nil, nil, nil, err
        }</span>

        <span class="cov8" title="1">bankRoute, err := uc.paymentRepo.GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment)
        if err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "get bank route fail", "error", err)
                return nil, nil, nil, err
        }</span>

        <span class="cov8" title="1">return &amp;account, purchase, bankRoute, nil</span>
}

func (uc *Usecase) handleSubmitRepaymentFailed(ctx context.Context, repayLog *model.RepaymentLog, submitErr error) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "handle failed submit repayment")

        repayLog = repayLog.HandlePartnerRepayError(submitErr)
        if err := uc.paymentRepo.UpdateRepaymentLogStatus(ctx, *repayLog); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "update repayment log fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">if err := uc.TriggerPollingRepayStatus(ctx, repayLog); err != nil </span><span class="cov8" title="1">{
                logger.Errorw("msg", "trigger polling repay status fail", "error", err)
                return err
        }</span>

        <span class="cov8" title="1">logger.Infow("msg", "trigger polling repay status success", "repay_log_id", repayLog.ID)

        return nil</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package refund

import (
        "context"
        "sync"

        "github.com/pkg/errors"
        "github.com/spf13/cast"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

const (
        channelSize              = 15
        inquirySize              = 50
        workerFundback           = 15
        FundbackOrderDescription = "Hoàn tiền lại cho khách hàng"
)

func (uc *Usecase) ExecuteFundback(ctx context.Context, settle *model.RefundSettle) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "execute fundback", "settle_id", settle.ID)

        if settle.Status != model.RefundSettleStatusSettled </span><span class="cov0" title="0">{
                return errors.New("settle status is not settled")
        }</span>
        <span class="cov0" title="0">if settle.GetPaybackObligationAmount() &lt;= 0 </span><span class="cov0" title="0">{
                stt := model.RefundSettleStatusCompleted
                err := uc.repo.UpdateRefundSettleStatusByID(ctx, settle.ID, stt)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "update refund settle status fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">return nil</span>
        }

        <span class="cov0" title="0">fbOrders, err := uc.orderRepo.ListRefundFundbackOrder(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "list refund fundback order fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">fbOrdersCast := model.RefundFundbackOrders(fbOrders)
        if fbOrdersCast.HasProcessingOrder() </span><span class="cov0" title="0">{
                logger.Infow("msg", "fundback order is processing")
                return nil
        }</span>

        <span class="cov0" title="0">tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "begin transaction fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        purchase, err := uc.paymentRepo.GetPaymentByTransID(tCtx, settle.ZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get payment by trans ID fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">zpTransID := settle.ZPTransID
        zalopayID := purchase.AccountInfo.ZalopayID
        fBackAmount := settle.GetPaybackObligationAmount()
        partnerCode := partner.CodeFromString(purchase.AccountInfo.PartnerCode)
        params := &amp;model.CreateRefundOrderRequest{
                Amount:       fBackAmount,
                ZaloPayID:    zalopayID,
                RefZPTransID: zpTransID,
                PartnerCode:  partnerCode,
                AppTransID:   idgen.GenAppTransID(),
                Description:  FundbackOrderDescription,
        }
        acOrder, err := uc.orderAdapter.CreateRefundFundbackOrder(tCtx, params)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create refund fundback order fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">fbOrder := model.NewRefundFundbackOrder(zalopayID, zpTransID, fBackAmount)
        fbOrder = fbOrder.EnrichFundbackOrder(settle, purchase).FulfillFundbackOrder(acOrder)
        fbOrder, err = uc.orderRepo.CreateRefundFundbackOrder(tCtx, fbOrder)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create refund fundback order fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">err = uc.orderAdapter.SubmitRefundFundbackOrder(tCtx, fbOrder)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "submit refund fundback order failed", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">if err := uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit transaction fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">uc.logger.Infow("msg", "fundback order created", "order_id", fbOrder.ID)

        return nil</span>
}

func (uc *Usecase) ProcessFundbackAfterSettlements(ctx context.Context) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        listSettleCh, err := uc.getRefundSettlesSuccess(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorf("failed to get refund settles: %v", err)
                return err
        }</span>
        <span class="cov0" title="0">if listSettleCh == nil </span><span class="cov0" title="0">{
                logger.Warn("no success refund settles to process")
                return nil
        }</span>

        <span class="cov0" title="0">waitGroup := new(sync.WaitGroup)
        waitGroup.Add(workerFundback)

        for i := range workerFundback </span><span class="cov0" title="0">{
                go func(id int) </span><span class="cov0" title="0">{
                        defer waitGroup.Done()
                        logger.Infow("msg", "worker process fundback", "worker_id", id)
                        uc.workerProcessFundback(ctx, listSettleCh)
                }</span>(i)
        }

        <span class="cov0" title="0">waitGroup.Wait()

        return nil</span>
}

type FundbackOrderUpdateRequest struct {
        AppID        int32
        AppTransID   string
        ZPTransID    int64
        OrderStatus  model.OrderStatus
        OriginStatus int
        ReasonStatus string
}

func (uc *Usecase) HandleFundbackOrderUpdate(ctx context.Context, request *FundbackOrderUpdateRequest) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "handle fundback order update", "request", request)

        order, err := uc.orderRepo.GetRefundFundbackOrder(ctx, request.AppTransID, request.AppID)
        if errors.Is(err, model.ErrOrderNotFound) </span><span class="cov0" title="0">{
                logger.Warnw("msg", "fundback order not found", "error", err)
                return nil
        }</span>
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get fundback order fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if order.IsFinal() </span><span class="cov0" title="0">{
                logger.Infow("msg", "fundback order is final", "order", order)
                return nil
        }</span>

        <span class="cov0" title="0">order.HandleOrderUpdate(
                request.ZPTransID,
                request.OrderStatus,
                request.ReasonStatus,
        )

        err = uc.txn.WithTx(ctx, func(tCtx context.Context) error </span><span class="cov0" title="0">{
                tErr := uc.orderRepo.UpdateOrderProgress(tCtx, model.OrderProgressUpdate{
                        OrderID:   order.ID,
                        Status:    order.Status,
                        ZpTransID: order.ZPTransID,
                        ExtraData: order.ExtraData,
                })
                if tErr != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "update order progress fail", "error", tErr)
                        return tErr
                }</span>
                <span class="cov0" title="0">if !order.IsSuccess() </span><span class="cov0" title="0">{
                        logger.Infow("msg", "fundback order is not success", "order", order)
                        return nil
                }</span>

                <span class="cov0" title="0">settleID := order.RefundSettleID
                settle, err := uc.repo.GetRefundSettleForUpdate(tCtx, settleID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "get refund settle for update fail", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">fdOrders, err := uc.orderRepo.ListRefundFundbackOrder(tCtx, settleID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "get fundback orders fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">fdOrdersCast := model.RefundFundbackOrders(fdOrders)
                totalPayback := fdOrdersCast.GetTotalSuccessAmount()
                settleStatus := settle.EvaluateStatusByPayback(totalPayback)

                tErr = uc.repo.UpdateRefundSettlePaybackInfo(
                        tCtx, settle.ID,
                        totalPayback, settleStatus,
                )
                if tErr != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "update refund settle payback info fail", "error", tErr)
                        return tErr
                }</span>

                <span class="cov0" title="0">logger.Infow("msg", "update refund settle payback info success", "settle_id", settle.ID)

                return nil</span>
        })
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "handle fundback order update fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "handle fundback order update success", "order_id", order.ID)

        return nil</span>
}

func (uc *Usecase) getRefundSettlesSuccess(ctx context.Context) (&lt;-chan *model.RefundSettle, error) <span class="cov0" title="0">{
        lastID := int64(0)
        logger := uc.logger.WithContext(ctx)
        result := make(chan *model.RefundSettle, channelSize)

        go func() </span><span class="cov0" title="0">{
                defer close(result)
                for </span><span class="cov0" title="0">{
                        cursor := cast.ToString(lastID)
                        pagination := &amp;model.Pagination{
                                Limit:  inquirySize,
                                Cursor: &amp;cursor,
                        }
                        listSettle, err := uc.repo.GetListRefundSettleByStatus(ctx, model.RefundSettleStatusSettled, pagination)
                        if err != nil </span><span class="cov0" title="0">{
                                logger.Errorw("msg", "get list refund settle fail", "error", err)
                                return
                        }</span>
                        <span class="cov0" title="0">if len(listSettle) == 0 </span><span class="cov0" title="0">{
                                // No more refund settle to process
                                logger.Infow("msg", "no more refund settle to process")
                                break</span>
                        }
                        <span class="cov0" title="0">for _, item := range listSettle </span><span class="cov0" title="0">{
                                result &lt;- item
                        }</span>
                        <span class="cov0" title="0">lastID = listSettle[len(listSettle)-1].ID</span>
                }
        }()

        <span class="cov0" title="0">return result, nil</span>
}

func (uc *Usecase) workerProcessFundback(ctx context.Context, dataCh &lt;-chan *model.RefundSettle) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        for settle := range dataCh </span><span class="cov0" title="0">{
                logger.Infow("msg", "process fundback", "settle_id", settle.ID)
                if err := uc.ExecuteFundback(ctx, settle); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "process fundback fail", "settle_id", settle.ID, "error", err)
                        continue</span>
                }
                <span class="cov0" title="0">logger.Infow("msg", "process fundback completed", "settle_id", settle.ID)</span>
        }
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package refund

import (
        "context"

        "github.com/pkg/errors"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "go.opentelemetry.io/otel"
        "go.opentelemetry.io/otel/trace"
)

func (uc *Usecase) PollingEarlyDischargeStatus(ctx context.Context, req *model.EarlyDischargeStatusWorkflowRequest) (*model.EarlyDischargeStatusWorkflowResponse, error) <span class="cov0" title="0">{
        var span trace.Span
        ctx, span = otel.GetTracerProvider().Tracer("payment-service").Start(ctx, "early-discharge-polling-status")
        defer span.End()

        logger := uc.logger.WithContext(ctx)
        logger.Infow("msg", "PollingEarlyDischargeStatus", "req", req)

        dischargeLog, err := uc.paymentRepo.GetEarlyDischargeLog(ctx, req.PaymentID, req.ZalopayID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollingEarlyDischargeStatus] get early discharge log fail", "req", req, "error", err)
                return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, get early discharge log fail")
        }</span>
        <span class="cov0" title="0">if dischargeLog.Status.IsFinalStatus() </span><span class="cov0" title="0">{
                logger.Infow("msg", "PollingEarlyDischargeStatus, discharge already in final status", "discharge_id", dischargeLog.ID, "status", dischargeLog.Status)
                return &amp;model.EarlyDischargeStatusWorkflowResponse{DischargeStatus: dischargeLog.Status.String()}, nil
        }</span>

        <span class="cov0" title="0">cimbTrans, err := uc.connector.InquiryTransaction(ctx, dischargeLog.PartnerReqID)
        if err != nil </span><span class="cov0" title="0">{
                // Handle inquiry transaction error, including "transaction not found" case
                return uc.handleDischargeTransactionError(ctx, err, dischargeLog)
        }</span>

        <span class="cov0" title="0">if !cimbTrans.PaymentStatus.IsFinalStatus() </span><span class="cov0" title="0">{
                logger.Infow("msg", "PollingEarlyDischargeStatus still processing, will retry", "discharge_id", dischargeLog.ID, "cimb_status", cimbTrans.PaymentStatus)
                return nil, errors.Errorf("PollingEarlyDischargeStatus still processing, will retry, cimb_status: %s", cimbTrans.PaymentStatus)
        }</span>

        <span class="cov0" title="0">dischargeLog.Status = model.FromCIMBPaymentStatusToPaymentStatus(cimbTrans.PaymentStatus)
        dischargeLog.PartnerData.RepaymentResult.RepaymentStatus = cimbTrans.PaymentStatus.String()

        switch dischargeLog.Status </span>{
        case model.PaymentStatusSucceeded:<span class="cov0" title="0">
                dischargeLog.PartnerData.RepaymentResult.CIMBError = model.CIMBError{}
                dischargeLog.PartnerData.RepaymentResult.BankTransID = cimbTrans.BankTransactionSequenceID</span>
        }

        <span class="cov0" title="0">tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "begin transaction fail", "error", err)
                return nil, errors.Wrap(err, "begin transaction fail")
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        _, err = uc.paymentRepo.UpdateEarlyDischargeLog(tCtx, *dischargeLog)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "PollingEarlyDischargeStatus, update early discharge log fail", "discharge_id", dischargeLog.ID, "status", dischargeLog.Status, "error", err)
                return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, update early discharge log fail")
        }</span>

        <span class="cov0" title="0">if err := uc.PublishRefundSettleResult(tCtx, dischargeLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "PollingEarlyDischargeStatus, publish refund settle result fail", "discharge_id", dischargeLog.ID, "status", dischargeLog.Status, "error", err)
                return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, publish refund settle result fail")
        }</span>

        <span class="cov0" title="0">if err := uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit transaction fail", "error", err)
                return nil, errors.Wrap(err, "commit transaction fail")
        }</span>

        <span class="cov0" title="0">response := &amp;model.EarlyDischargeStatusWorkflowResponse{
                DischargeStatus:        dischargeLog.Status.String(),
                PartnerDischargeStatus: cimbTrans.PaymentStatus.String(),
        }

        logger.Infow("msg", "PollingEarlyDischargeStatus completed",
                "discharge_id", dischargeLog.ID,
                "status", dischargeLog.Status,
        )

        return response, nil</span>
}

func (uc *Usecase) PollRepaymentStatus(ctx context.Context, req *model.RepaymentStatusPollingRequest) (*model.RepaymentStatusPollingResponse, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        repayLog, err := uc.paymentRepo.GetRepaymentByID(ctx, req.PaymentID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get repayment log fail", "error", err)
                return nil, err
        }</span>

        // Check if repayment is already in final status
        <span class="cov0" title="0">if repayLog.Status.IsFinalStatus() </span><span class="cov0" title="0">{
                logger.Infow("msg", "repayment already in final status", "repay_log_id", repayLog.ID, "status", repayLog.Status)
                return &amp;model.RepaymentStatusPollingResponse{RepayStatus: repayLog.Status.String()}, nil
        }</span>

        <span class="cov0" title="0">transStatus, err := uc.connector.InquiryTransaction(ctx, repayLog.PartnerReqID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "inquiry transaction fail", "error", err)
                return uc.handleRepaymentTransactionError(ctx, err, repayLog)
        }</span>
        <span class="cov0" title="0">if !transStatus.PaymentStatus.IsFinalStatus() </span><span class="cov0" title="0">{
                logger.Infow("msg", "PollingRepaymentStatus still processing, will retry", "cimb_status", transStatus.PaymentStatus)
                return nil, errors.Errorf("PollingRepaymentStatus still processing, will retry, cimb_status: %s", transStatus.PaymentStatus)
        }</span>

        // Update repayment log with new status
        <span class="cov0" title="0">repayLog.Status = model.FromCIMBPaymentStatusToPaymentStatus(transStatus.PaymentStatus)
        repayLog.PartnerData.RepaymentResult.RepaymentStatus = transStatus.PaymentStatus.String()

        switch repayLog.Status </span>{
        case model.PaymentStatusSucceeded:<span class="cov0" title="0">
                repayLog.PartnerData.RepaymentResult.CIMBError = model.CIMBError{}
                repayLog.PartnerData.RepaymentResult.BankTransID = transStatus.BankTransactionSequenceID</span>
        }

        <span class="cov0" title="0">tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "begin transaction fail", "error", err)
                return nil, errors.Wrap(err, "begin transaction fail")
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        repayLog, err = uc.paymentRepo.GetRepaymentByID(tCtx, req.PaymentID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get repayment log fail", "error", err)
                return nil, errors.Wrap(err, "get repayment log fail")
        }</span>

        <span class="cov0" title="0">if err := uc.paymentRepo.UpdateRepaymentLogStatus(tCtx, *repayLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update repayment log status fail", "error", err)
                return nil, errors.Wrap(err, "update repayment log status fail")
        }</span>

        <span class="cov0" title="0">if err := uc.PublishRefundExpiredResult(tCtx, repayLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "publish refund expired result fail", "error", err)
                return nil, errors.Wrap(err, "publish refund expired result fail")
        }</span>

        <span class="cov0" title="0">if err := uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit transaction fail", "error", err)
                return nil, errors.Wrap(err, "commit transaction fail")
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "publish refund expired result success", "repay_log_id", repayLog.ID)

        return &amp;model.RepaymentStatusPollingResponse{
                RepayStatus:   repayLog.Status.String(),
                PartnerStatus: transStatus.PaymentStatus.String(),
        }, nil</span>
}

func (uc *Usecase) handleDischargeTransactionError(ctx context.Context, iErr error, dischargeLog *model.EarlyDischargeLog) (*model.EarlyDischargeStatusWorkflowResponse, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Errorw("msg", "[PollingEarlyDischargeStatus] inquiry transaction fail", "discharge_id", dischargeLog.ID, "error", iErr)

        if !errors.Is(iErr, model.ErrPartnerTransactionNotFound) </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollingEarlyDischargeStatus] inquiry transaction fail", "discharge_id", dischargeLog.ID, "error", iErr)
                return nil, errors.Wrap(iErr, "PollingEarlyDischargeStatus, inquiry transaction fail")
        }</span>

        // Check if the error is specifically a transaction not found error
        <span class="cov0" title="0">logger.Warnw("msg", "[PollingEarlyDischargeStatus] transaction not found in partner system, marking as failed",
                "discharge_id", dischargeLog.ID, "error", iErr)

        // Update the discharge log status to failed
        dischargeLog.Status = model.PaymentStatusFailed
        dischargeLog.PartnerData.RepaymentResult.BankTransID = ""
        dischargeLog.PartnerData.RepaymentResult.RepaymentStatus = ""
        dischargeLog.PartnerData.RepaymentResult.CIMBError.
                SetErrorCodeIfNotExist("transaction_not_found").
                AppendErrorDescription(iErr.Error())

        tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollingEarlyDischargeStatus] begin transaction fail", "error", err)
                return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, begin transaction fail")
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        _, updErr := uc.paymentRepo.UpdateEarlyDischargeLog(tCtx, *dischargeLog)
        if updErr != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollingEarlyDischargeStatus] update early discharge log to failed status failed",
                        "discharge_id", dischargeLog.ID, "error", updErr)
                return nil, errors.Wrap(updErr, "PollingEarlyDischargeStatus, update early discharge log to failed status failed")
        }</span>

        <span class="cov0" title="0">if err := uc.PublishRefundSettleResult(tCtx, dischargeLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollingEarlyDischargeStatus] publish refund settle result failed", "discharge_id", dischargeLog.ID, "error", err)
                return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, publish refund settle result failed")
        }</span>

        <span class="cov0" title="0">if err := uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollingEarlyDischargeStatus] commit transaction fail", "error", err)
                return nil, errors.Wrap(err, "PollingEarlyDischargeStatus, commit transaction fail")
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "[PollingEarlyDischargeStatus] publish refund settle result success", "discharge_id", dischargeLog.ID)

        return &amp;model.EarlyDischargeStatusWorkflowResponse{
                DischargeStatus:        dischargeLog.Status.String(),
                ErrorDescription:       iErr.Error(),
                PartnerDischargeStatus: model.CIMBPaymentStatusTransferFailed.String(),
        }, nil</span>
}

func (uc *Usecase) handleRepaymentTransactionError(ctx context.Context, iErr error, repayLog *model.RepaymentLog) (*model.RepaymentStatusPollingResponse, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Errorw("msg", "[PollRepaymentStatus] inquiry transaction fail", "repay_log_id", repayLog.ID, "error", iErr)

        if !errors.Is(iErr, model.ErrPartnerTransactionNotFound) </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollRepaymentStatus] inquiry transaction fail", "repay_log_id", repayLog.ID, "error", iErr)
                return nil, errors.Wrap(iErr, "PollRepaymentStatus, inquiry transaction fail")
        }</span>

        // Check if the error is specifically a transaction not found error
        <span class="cov0" title="0">logger.Warnw("msg", "[PollRepaymentStatus] transaction not found in partner system, marking as failed",
                "repay_log_id", repayLog.ID, "error", iErr)

        // Update the repayment log status to failed
        repayLog.Status = model.PaymentStatusFailed
        repayLog.PartnerData.RepaymentResult.BankTransID = ""
        repayLog.PartnerData.RepaymentResult.RepaymentStatus = ""
        repayLog.PartnerData.RepaymentResult.CIMBError.
                SetErrorCodeIfNotExist("transaction_not_found").
                AppendErrorDescription(iErr.Error())

        tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollRepaymentStatus] begin transaction fail", "error", err)
                return nil, errors.Wrap(err, "PollRepaymentStatus, begin transaction fail")
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        if err := uc.paymentRepo.UpdateRepaymentLogStatus(tCtx, *repayLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollRepaymentStatus] update repayment log to failed status failed",
                        "repay_log_id", repayLog.ID, "error", err)
                return nil, errors.Wrap(err, "PollRepaymentStatus, update repayment log to failed status failed")
        }</span>

        <span class="cov0" title="0">if err := uc.PublishRefundExpiredResult(tCtx, repayLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "[PollRepaymentStatus] publish refund expired result failed", "repay_log_id", repayLog.ID, "error", err)
                return nil, errors.Wrap(err, "PollRepaymentStatus, publish refund expired result failed")
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "[PollRepaymentStatus] publish refund expired result success", "repay_log_id", repayLog.ID)

        return &amp;model.RepaymentStatusPollingResponse{
                RepayStatus:      repayLog.Status.String(),
                PartnerStatus:    model.CIMBPaymentStatusTransferFailed.String(),
                ErrorDescription: iErr.Error(),
        }, nil</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">package refund

import (
        "context"
        "fmt"

        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

type PreCheckResponse struct {
        RefundOrder  *model.RefundOrder
        SettleStatus model.SettleStatus
}

func (uc *Usecase) PreCheck(ctx context.Context, req model.RefundOrder) (*PreCheckResponse, error) <span class="cov0" title="0">{
        uc.logger.WithContext(ctx).Infow("msg", "PreCheck", "req", req)

        payment, err := uc.paymentRepo.GetPaymentByTransID(ctx, req.ZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if payment.Status != model.PaymentStatusSucceeded </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
                return nil, fmt.Errorf("payment status is not success")
        }</span>

        // Check Refund Condition from Partner
        <span class="cov0" title="0">instStatus, err := uc.installmentAdapter.GetInstallmentStatus(ctx, req.ZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "get installment status fail", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if !instStatus.Status.IsValid() </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "get installment status fail", "error", "invalid status", "raw_status", instStatus.Status)
                return nil, fmt.Errorf("invalid status: %v", instStatus.Status)
        }</span>

        <span class="cov0" title="0">req.Status = model.RefundStatusInit
        req.RefundType = model.RefundTypeAuto

        switch instStatus.Status </span>{
        case model.InstallmentStatusOpen, model.InstallmentStatusInCreation:<span class="cov0" title="0">
                req.ProcessType = model.RefundProcessTypeSettlement</span>
        case model.InstallmentStatusClosed:<span class="cov0" title="0">
                req.ProcessType = model.RefundProcessTypeFundback</span>
        }

        // Insert RefundLogs
        <span class="cov0" title="0">refundOrder, err := uc.repo.CreateRefundLog(ctx, req)
        if err != nil </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "insert refund logs to DB fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">settleStatus := model.FromInstallmentToSettleStatus(instStatus.Status)

        return &amp;PreCheckResponse{
                RefundOrder:  refundOrder,
                SettleStatus: settleStatus,
        }, nil</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package refund

import (
        "context"
        "time"

        "github.com/avast/retry-go"
        "github.com/pkg/errors"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
)

func (uc *Usecase) TriggerJobReconcileRefundSettlements(ctx context.Context, zpTransID int64) error <span class="cov8" title="1">{
        logger := uc.logger.WithContext(ctx)
        retryOpts := []retry.Option{
                retry.Attempts(3),
                retry.Delay(3 * time.Second),
        }

        err := retry.Do(func() error </span><span class="cov8" title="1">{
                params := &amp;model.RefundSettleReconParams{ZPTransID: zpTransID}
                if err := uc.taskJob.ExecuteReconcileRefundSettleJob(ctx, params); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "trigger reconcile refund settlements fail", "error", err)
                        return err
                }</span>
                <span class="cov8" title="1">return nil</span>
        }, retryOpts...)
        <span class="cov8" title="1">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "trigger reconcile refund settlements fail", "error", err)
                return err
        }</span>
        <span class="cov8" title="1">return nil</span>
}

func (uc *Usecase) ReconcileRefundSettlements(ctx context.Context, zpTransID int64) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        earlyDischarge, err := uc.installmentAdapter.GetEarlyDischarge(ctx, zpTransID, true)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get early discharge fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if err = earlyDischarge.Validate(); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "validate early discharge fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">refundLogs, err := uc.repo.GetRefundLogsByZPTransID(ctx, zpTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund log settlements fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if len(refundLogs) == 0 </span><span class="cov0" title="0">{
                logger.Infow("msg", "no refund log settlements found")
                return errors.New("no refund log settlements found")
        }</span>

        // Calculate total refund amount
        <span class="cov0" title="0">refundLogsCast := model.RefundOrders(refundLogs)
        dischargeAmount := earlyDischarge.TotalAmount
        netRefundAmount := refundLogsCast.SumNetRefundAmount()
        totalRefundAmount := refundLogsCast.SumTotalRefundAmount()
        refundRecon := &amp;model.RefundSettleRecon{
                NetRefundAmount:   netRefundAmount,
                TotalRefundAmount: totalRefundAmount,
                SettlementAmount:  dischargeAmount,
        }

        // Get current refund settle
        refundSettle, err := uc.repo.GetRefundSettleByZPTransID(ctx, zpTransID)
        if err != nil &amp;&amp; !errors.Is(err, model.ErrRefundSettleNotFound) </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if refundSettle == nil </span><span class="cov0" title="0">{
                if refundRecon.NetRefundAmount == 0 </span><span class="cov0" title="0">{
                        logger.Infow("msg", "refund amount is zero, no need to create reconcile")
                        return nil
                }</span>

                <span class="cov0" title="0">orgPurchase, err := uc.paymentRepo.GetPaymentByTransID(ctx, zpTransID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "get purchase fail", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">refundSettle := model.NewRefundSettle(zpTransID, refundRecon)
                refundSettle = refundSettle.StorePurchaseSnapshot(orgPurchase)
                refundSettle, err = uc.createRefundSettle(ctx, refundSettle)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "create refund settle fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">logger.Infow("msg", "create refund settle success", "refundSettle", refundSettle)
                return nil</span>
        }

        <span class="cov0" title="0">logger.Infow("msg", "current refund settle info", "refundSettle", refundSettle)

        if !refundSettle.HasSnapshotChanged() &amp;&amp; !refundSettle.HasReconAmountsChanged(refundRecon) </span><span class="cov0" title="0">{
                logger.Infow("msg", "refund settle snapshot and recon amount not changed")
                return nil
        }</span>

        // Update refund settle after recon
        <span class="cov0" title="0">refundSettle, err = uc.updateRefundAfterRecon(ctx, refundSettle, refundRecon)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update refund settle fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "reconcile refund settle success", "refundSettle", refundSettle)

        return nil</span>
}

func (uc *Usecase) createRefundSettle(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        if settle == nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "settle is nil")
                return nil, errors.New("settle is nil")
        }</span>

        <span class="cov0" title="0">settle, err := uc.repo.CreateRefundSettle(ctx, settle)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">err = uc.DispatchRefundSettleChanged(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "dispatch refund settle changed fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">err = uc.ProcessRefundSettleInfo(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "process refund settle info fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">return settle, nil</span>
}

func (uc *Usecase) updateRefundAfterRecon(
        ctx context.Context,
        settle *model.RefundSettle,
        recon *model.RefundSettleRecon) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        if settle == nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "settle is nil")
                return nil, errors.New("settle is nil")
        }</span>

        <span class="cov0" title="0">tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "begin tx fail", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        settle, err = uc.repo.GetRefundSettleForUpdate(tCtx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">settle = settle.SyncReconAmounts(recon)
        err = uc.repo.UpdateRefundSettleReconAmts(tCtx, settle)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit tx fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">err = uc.DispatchRefundSettleChanged(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "dispatch refund settle changed fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">err = uc.ProcessRefundSettleInfo(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "process refund settle info fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "update refund settle success", "refundSettle", settle)

        return settle, nil</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package refund

import (
        "context"
        "fmt"
        "time"

        "github.com/go-kratos/kratos/v2/log"
        "gitlab.zalopay.vn/fin/installment/payment-service/configs"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/types"
)

type Usecase struct {
        logger             *log.Helper
        repo               types.RefundRepo
        txn                types.Transaction
        orderCfgs          *configs.OrderConfigsHelper
        refundCfg          *configs.Refund
        orderRepo          types.OrderRepo
        paymentRepo        types.PaymentRepo
        taskJob            types.TaskJobAdapter
        distLock           types.DistributedLock
        connector          types.PartnerConnector
        settleNotifier     types.RefundSettleNotifier
        orderAdapter       types.AcquiringCoreAdapter
        accountAdapter     types.AccountAdapter
        installmentAdapter types.InstallmentAdapter
}

func NewRefundUsecase(logger log.Logger,
        repo types.RefundRepo,
        txn types.Transaction,
        taskJob types.TaskJobAdapter,
        distLock types.DistributedLock,
        rootCfg *configs.Payment,
        orderCfgs *configs.OrderConfigsHelper,
        orderRepo types.OrderRepo,
        paymentRepo types.PaymentRepo,
        connector types.PartnerConnector,
        orderAdapter types.AcquiringCoreAdapter,
        accountAdapter types.AccountAdapter,
        refundSettleNotifier types.RefundSettleNotifier,
        installmentAdapter types.InstallmentAdapter) *Usecase <span class="cov8" title="1">{
        return &amp;Usecase{
                logger:             log.NewHelper(log.With(logger, "module", "RefundUsecase")),
                txn:                txn,
                repo:               repo,
                taskJob:            taskJob,
                distLock:           distLock,
                connector:          connector,
                orderCfgs:          orderCfgs,
                refundCfg:          rootCfg.Refund,
                orderRepo:          orderRepo,
                paymentRepo:        paymentRepo,
                accountAdapter:     accountAdapter,
                orderAdapter:       orderAdapter,
                installmentAdapter: installmentAdapter,
                settleNotifier:     refundSettleNotifier,
        }
}</span>

func (uc *Usecase) Refund(ctx context.Context, req model.RefundOrder) (*model.RefundOrder, error) <span class="cov0" title="0">{
        uc.logger.WithContext(ctx).Infow("msg", "Refund", "req", req)
        payment, err := uc.paymentRepo.GetPaymentByTransID(ctx, req.ZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if payment.Status != model.PaymentStatusSucceeded </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "", "error", err)
                return nil, fmt.Errorf("payment status is not success")
        }</span>

        <span class="cov0" title="0">req.Status = model.RefundStatusSuccess
        req.RefundType = model.RefundTypeManual
        req.ProcessType = model.RefundProcessTypeManual
        // Insert RefundLogs
        refundOrder, err := uc.repo.CreateRefundLog(ctx, req)
        if err != nil </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "insert refund logs to DB fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">return refundOrder, nil</span>
}

func (uc *Usecase) RefundQuery(ctx context.Context, refundID int64) (*model.RefundOrder, error) <span class="cov0" title="0">{
        refundLog, err := uc.repo.GetRefundLogByRefundID(ctx, refundID)
        if err != nil </span><span class="cov0" title="0">{
                uc.logger.WithContext(ctx).Errorw("msg", "get refund log fail", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">return refundLog, nil</span>
}

func (uc *Usecase) ProcessCompletion(ctx context.Context, refundID int64, finalStatus model.RefundStatus) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        if !finalStatus.IsFinal() </span><span class="cov0" title="0">{
                logger.Errorw("msg", "invalid final status", "finalStatus", finalStatus)
                return fmt.Errorf("invalid final status")
        }</span>

        <span class="cov0" title="0">refundLog, err := uc.repo.GetRefundLogByRefundID(ctx, refundID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund log fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if refundLog.IsComplete() </span><span class="cov0" title="0">{
                logger.Infow("msg", "refund log is already completed", "refundLog", refundLog)
                return nil
        }</span>

        <span class="cov0" title="0">refundLog.Status = finalStatus
        if refundLog.IsSuccess() </span><span class="cov0" title="0">{
                expiredDuration := uc.refundCfg.TransAliveIn.AsDuration()
                refundLog.DeadlineAt = time.Now().Add(expiredDuration)
        }</span>

        <span class="cov0" title="0">if err = uc.repo.CompleteRefundLog(ctx, refundLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "complete refund log fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">go func() </span><span class="cov0" title="0">{
                if !refundLog.IsSuccess() </span><span class="cov0" title="0">{
                        logger.Warnw("msg", "refund log is not success", "refundLog", refundLog)
                        return
                }</span>
                <span class="cov0" title="0">if refundLog.ProcessType != model.RefundProcessTypeSettlement </span><span class="cov0" title="0">{
                        logger.Infow("msg", "refund log is not settlement", "refundLog", refundLog)
                        return
                }</span>
                <span class="cov0" title="0">ctx = context.WithoutCancel(ctx)
                err := uc.TriggerJobReconcileRefundSettlements(ctx, refundLog.ZPTransID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "trigger job reconcile refund settlements fail", "error", err)
                }</span>
        }()

        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package refund

import (
        "context"

        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/partner"
)

func (uc *Usecase) RetrySubmitSettleOrder(ctx context.Context, settleID int64) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        settle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get settle info", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if settle.Status != model.RefundSettleStatusProcessing </span><span class="cov0" title="0">{
                logger.Warnw("msg", "Settle order not in processing status")
                return nil
        }</span>

        <span class="cov0" title="0">orders, err := uc.orderRepo.ListRefundSettleOrder(ctx, settleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get settle orders", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">ordersCast := model.RefundSettleOrders(orders).SortByIDAsc()
        if !ordersCast.HasLastOrderFailed() </span><span class="cov0" title="0">{
                logger.Warnw("msg", "No failed order to retry")
                return nil
        }</span>
        <span class="cov0" title="0">if !ordersCast.EligibleToRetrySubmit() </span><span class="cov0" title="0">{
                logger.Warnw("msg", "Settle order not eligible to retry submit")
                return nil
        }</span>

        <span class="cov0" title="0">err = uc.txn.WithTx(ctx, func(tCtx context.Context) error </span><span class="cov0" title="0">{
                lastOrder := ordersCast.MustGetLastOrder()
                newOrder := lastOrder.CloneForResubmission()
                acOrder, tErr := uc.orderAdapter.CreateRefundSettleOrder(tCtx, &amp;model.CreateRefundOrderRequest{
                        Amount:       newOrder.Amount,
                        ZaloPayID:    newOrder.ZalopayID,
                        AppTransID:   idgen.GenAppTransID(),
                        Description:  newOrder.Description,
                        RefZPTransID: newOrder.GetRefZPTransID(),
                        PartnerCode:  partner.PartnerCode(newOrder.PartnerCode),
                })
                if tErr != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to create refund settle order", "error", tErr)
                        return tErr
                }</span>

                <span class="cov0" title="0">newOrder = newOrder.WithStatus(model.OrderStatusPending).FulfillSettleOrder(acOrder)
                newOrder, tErr = uc.orderRepo.CreateRefundSettleOrder(tCtx, newOrder)
                if tErr != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to create refund settle order", "error", tErr)
                        return tErr
                }</span>

                <span class="cov0" title="0">eadLog, err := uc.paymentRepo.GetEarlyDischargeLogByOrderID(tCtx, lastOrder.ID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to get early discharge log", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">eadLog = eadLog.WithRefundSettleOrder(newOrder)
                err = uc.paymentRepo.UpdatePaymentLogOrderIDs(tCtx, eadLog.ID, eadLog.Order)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update early discharge log", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">err = uc.orderAdapter.SubmitRefundSettleOrder(tCtx, newOrder)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to submit refund settle order", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">return nil</span>
        })
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to retry submit settle order", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "Retry submit settle order success", "settle_id", settleID)

        return nil</span>
}
</pre>
		
		<pre class="file" id="file8" style="display: none">package refund

import (
        "context"
        "time"

        "github.com/avast/retry-go"
        "github.com/pkg/errors"
        "github.com/spf13/cast"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
        SettleOrderDescription = "Tất toán khoản vay"
)

func (uc *Usecase) ProcessRefundSettleInfo(ctx context.Context, settleID int64) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "begin or reuse tx fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        refundSettle, err := uc.repo.GetRefundSettleForUpdate(tCtx, settleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if !refundSettle.ShouldTriggerSettlement() </span><span class="cov0" title="0">{
                logger.Infow("msg", "not have condition to trigger settlement", "refundSettle", refundSettle)
                return nil
        }</span>

        <span class="cov0" title="0">err = uc.repo.UpdateRefundSettleStatusByID(tCtx, settleID, model.RefundSettleStatusPending)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update refund settle status fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit tx fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">err = uc.PublishRefundSettleRequest(ctx, refundSettle)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "publish refund settle request fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">return nil</span>
}

func (uc *Usecase) DispatchRefundSettleChanged(ctx context.Context, settleID int64) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)
        retryOpts := []retry.Option{
                retry.Attempts(3),
                retry.Delay(500 * time.Millisecond),
        }
        err := retry.Do(func() error </span><span class="cov0" title="0">{
                tCtx, err := uc.txn.BeginTx(ctx)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "begin tx fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

                refundSettle, err := uc.repo.GetRefundSettleByID(tCtx, settleID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "get refund settle fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">if !refundSettle.HasSnapshotChanged() </span><span class="cov0" title="0">{
                        logger.Infow("msg", "refund settle snapshot not changed")
                        if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                                logger.Errorw("msg", "commit tx fail", "error", err)
                                return err
                        }</span>
                        <span class="cov0" title="0">return nil</span>
                }

                <span class="cov0" title="0">originVersion := refundSettle.RefreshSnapshotData()
                if err = uc.repo.UpdateRefundSettleEventData(tCtx, refundSettle, originVersion); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "update refund settle fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">if err = uc.installmentAdapter.NotifyInstallmentRefund(tCtx, refundSettle); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "notify installment refund fail", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "commit tx fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">return nil</span>
        }, retryOpts...)

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "dispatch refund settle changed fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">return nil</span>
}

// syncSettlementAmountIfChanged is a helper method that checks if settlement amount has changed
// and updates it in the database if needed
func (uc *Usecase) syncSettlementAmountIfChanged(ctx context.Context, settle *model.RefundSettle, earlyDischarge model.EarlyDischarge) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        if settle.SettlementAmount == earlyDischarge.TotalAmount </span><span class="cov0" title="0">{
                logger.Infow("msg", "settlement amount not changed")
                return settle, nil
        }</span>

        <span class="cov0" title="0">settle, err := uc.repo.GetRefundSettleByID(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">err = uc.repo.UpdateRefundSettlementAmount(ctx, settle.ID, earlyDischarge.TotalAmount)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">return settle.SetSettlementAmount(earlyDischarge.TotalAmount), nil</span>
}

func (uc *Usecase) SyncLatestSettlementAmount(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        earlyDischarge, err := uc.installmentAdapter.GetEarlyDischarge(ctx, settle.ZPTransID, true)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get early discharge fail", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if err = earlyDischarge.Validate(); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "validate early discharge fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">return uc.syncSettlementAmountIfChanged(ctx, settle, earlyDischarge)</span>
}

func (uc *Usecase) PublishRefundSettleRequest(ctx context.Context, settle *model.RefundSettle) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        if err := uc.settleNotifier.PublishRefundSettleEvent(ctx, settle); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "publish refund settle event fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "refund settle event published successfully", "settleInfo", settle)

        return nil</span>
}

func (uc *Usecase) PublishRefundSettleResult(ctx context.Context, eadLog *model.EarlyDischargeLog) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        order, err := uc.orderRepo.GetRefundSettleOrder(ctx, eadLog.Order.AppTransID, eadLog.Order.AppID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle order fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">settle, err := uc.repo.GetRefundSettleByID(ctx, order.RefundSettleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">if err := uc.settleNotifier.PublishRefundSettleResult(ctx, settle, eadLog); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "publish refund settle event fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "refund settle event published successfully", "settleInfo", settle)

        return nil</span>
}

func (uc *Usecase) GetRefundSettleInfo(ctx context.Context, settleID int64) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        settle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">return settle, nil</span>
}

func (uc *Usecase) GetRefundSettleByZPTransID(ctx context.Context, zpTransID int64) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        settle, err := uc.repo.GetRefundSettleByZPTransID(ctx, zpTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">return settle, nil</span>
}

func (uc *Usecase) ExecuteRefundSettlement(ctx context.Context, settleID int64) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        settle, err := uc.repo.GetRefundSettleByID(ctx, settleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get refund settle fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if !settle.CanStartSettlement() </span><span class="cov0" title="0">{
                logger.Infow("msg", "refund settlement cannot start", "settleID", settle.ID, "status", settle.Status)
                return nil
        }</span>

        <span class="cov0" title="0">settle, err = uc.SyncLatestSettlementAmount(ctx, settle)
        if err != nil </span><span class="cov0" title="0">{
                logger.Warnw("msg", "sync latest settlement amount fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if !settle.HasSufficientFunds() </span><span class="cov0" title="0">{
                logger.Infow("msg", "not have sufficient funds to trigger settlement", "settle", settle)
                uc.TriggerJobReconcileRefundSettlements(ctx, settle.ZPTransID)
                return model.ErrInsufficientSettlementFunds
        }</span>

        <span class="cov0" title="0">account, purchase, bankRoute, err := uc.getResourceForPreparingStandardSettle(ctx, settle.ZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get resource for preparing standard settle fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">tCtx, err := uc.txn.BeginTx(ctx)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "begin tx fail", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">defer uc.txn.RollbackTx(tCtx)

        if err = uc.repo.UpdateRefundSettleStatusByID(tCtx, settle.ID, model.RefundSettleStatusProcessing); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update refund settle status fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">order, err := uc.buildRefundSettleOrder(settle, purchase)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "build refund settle order fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">order, err = uc.orderRepo.CreateRefundSettleOrder(tCtx, order)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create refund settle order fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">dischargeLog := model.NewEarlyDischargeLog(order, bankRoute, account)
        dischargeLog, err = uc.paymentRepo.CreateEarlyDischargeLog(tCtx, *dischargeLog)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "create early discharge log fail", "error", err, "discharge_log", dischargeLog)
                return err
        }</span>

        <span class="cov0" title="0">if err = uc.txn.CommitTx(tCtx); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "commit tx fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">installmentStatus, err := uc.installmentAdapter.GetInstallmentStatus(ctx, cast.ToInt64(purchase.ZpTransID))
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get installment status fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">earlyDischargeReq := model.EarlyDischargeRequest{
                PaymentID:          dischargeLog.ID,
                RequestID:          dischargeLog.PartnerReqID,
                LoanID:             installmentStatus.LoanID,
                Amount:             dischargeLog.Order.Amount,
                PaymentDescription: SettleOrderDescription,
                CorpAccountNumber:  dischargeLog.BankRoute.BankAccountNumber,
                CorpAccountName:    dischargeLog.BankRoute.BankAccountName,
        }

        result, err := uc.connector.SubmitEarlyDischarge(ctx, earlyDischargeReq)
        if err != nil || result.IsError() </span><span class="cov0" title="0">{
                if err == nil </span><span class="cov0" title="0">{
                        err = result.CIMBError
                }</span>
                <span class="cov0" title="0">logger.Errorw("msg", "submit early discharge fail", "error", err, "req", earlyDischargeReq)
                return uc.handleSubmitEarlyDischargeError(ctx, err, dischargeLog)</span>
        }

        <span class="cov0" title="0">err = uc.handleSubmitEarlyDischargeResult(ctx, settle, result, dischargeLog)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "handle early discharge result fail", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">return nil</span>
}

// handleSubmitEarlyDischargeError sets error information in the discharge log
// This is a simple helper method that doesn't handle transactions or trigger polling
func (uc *Usecase) handleSubmitEarlyDischargeError(ctx context.Context, submitErr error, dischargeLog *model.EarlyDischargeLog) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "handle early discharge submission error info", "discharge_id", dischargeLog.ID, "error", submitErr)

        // Set error info in the discharge log
        dischargeLog.Status = model.PaymentStatusPending
        dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode = "submit_early_discharge_error"
        dischargeLog.PartnerData.RepaymentResult.CIMBError.Description = submitErr.Error()

        dischargeLog, err := uc.paymentRepo.UpdateEarlyDischargeLog(ctx, *dischargeLog)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update early discharge log fail", "error", err)
                return errors.Wrap(err, "failed to update early discharge log")
        }</span>

        // Trigger polling regardless of submission result
        <span class="cov0" title="0">err = uc.TriggerPollingEarlyDischargeStatus(ctx, dischargeLog.ID, dischargeLog.AccountInfo.ZalopayID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "trigger polling early discharge status fail", "error", err)
                return errors.Wrap(err, "failed to trigger polling early discharge status")
        }</span>

        <span class="cov0" title="0">logger.Errorw("msg", "Handled early discharge submission error", "discharge_id", dischargeLog.ID)

        return nil</span>
}

func (uc *Usecase) handleSubmitEarlyDischargeResult(ctx context.Context, settle *model.RefundSettle, result *model.EarlyDischargeResult, dischargeLog *model.EarlyDischargeLog) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "handle early discharge submission result", "result", *result)

        needReconcilation := result.NeedReconcilation
        dischargeLog.Status = model.FromCIMBPaymentStatusToPaymentStatus(result.TransactionStatus)
        switch dischargeLog.Status </span>{
        case model.PaymentStatusSucceeded:<span class="cov0" title="0">
                dischargeLog.PartnerData.RepaymentResult.CIMBError = model.CIMBError{}</span>
        case model.PaymentStatusFailed:<span class="cov0" title="0">
                dischargeLog.PartnerData.RepaymentResult.CIMBError.ErrorCode = result.ErrorCode
                dischargeLog.PartnerData.RepaymentResult.CIMBError.Description = result.ErrorCode</span>
        }

        <span class="cov0" title="0">err := uc.paymentRepo.UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, *dischargeLog)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update early discharge log fail", "error", err)
                return errors.Wrap(err, "failed to update early discharge log status")
        }</span>

        <span class="cov0" title="0">switch dischargeLog.Status </span>{
        case model.PaymentStatusSucceeded, model.PaymentStatusFailed:<span class="cov0" title="0">
                if err = uc.PublishRefundSettleResult(ctx, dischargeLog); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "publish refund settle result fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">logger.Infow("msg", "publish refund settle result success", "discharge_id", dischargeLog.ID)

                if !needReconcilation </span><span class="cov0" title="0">{
                        return nil
                }</span>
                <span class="cov0" title="0">if err := uc.TriggerJobReconcileRefundSettlements(ctx, settle.ZPTransID); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "trigger job reconcile refund settlements fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">logger.Infow("msg", "trigger job reconcile refund settlements success", "discharge_id", dischargeLog.ID)

                return nil</span>
        case model.PaymentStatusPending, model.PaymentStatusProcessing:<span class="cov0" title="0">
                paymentID := dischargeLog.ID
                zalopayID := dischargeLog.AccountInfo.ZalopayID
                if err = uc.TriggerPollingEarlyDischargeStatus(ctx, paymentID, zalopayID); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "trigger polling early discharge status fail", "error", err)
                        return err
                }</span>
                <span class="cov0" title="0">logger.Infow("msg", "trigger polling early discharge status success", "discharge_id", dischargeLog.ID)
                return nil</span>
        default:<span class="cov0" title="0">
                logger.Errorw("msg", "invalid discharge log status", "status", dischargeLog.Status)
                return errors.New("invalid discharge log status")</span>
        }
}

type RefundSettleResult struct {
        AppID               int32
        OrderID             int64
        AppTransID          string
        RefTransID          int64
        SettlePaymentID     int64
        SettlePaymentStatus model.PaymentStatus
}

func (uc *Usecase) ProcessRefundSettleResult(ctx context.Context, event *RefundSettleResult) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        logger.Infow("msg", "Processing refund settle response", "event", event)

        if !event.SettlePaymentStatus.IsFinalStatus() </span><span class="cov0" title="0">{
                logger.Infow("msg", "Ignoring non-final status", "status", event.SettlePaymentStatus)
                return nil
        }</span>

        <span class="cov0" title="0">order, err := uc.orderRepo.GetRefundSettleOrder(ctx, event.AppTransID, event.AppID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get refund settle order", "error", err)
                return errors.Wrap(err, "failed to get refund settle order")
        }</span>
        <span class="cov0" title="0">if order == nil </span><span class="cov0" title="0">{
                logger.Warnw("msg", "Refund settle order not found", "app_id", event.AppID, "app_trans_id", event.AppTransID)
                return nil
        }</span>

        <span class="cov0" title="0">if event.SettlePaymentStatus.IsFailedStatus() </span><span class="cov0" title="0">{
                logger.Infow("msg", "Refund settle order failed", "order_id", order.ID, "app_trans_id", event.AppTransID)
                return uc.handleRefundSettleFailed(ctx, order, event)
        }</span>

        <span class="cov0" title="0">err = uc.txn.WithTx(ctx, func(ctx context.Context) error </span><span class="cov0" title="0">{
                // Create refund settle order
                acOrder, err := uc.orderAdapter.CreateRefundSettleOrder(ctx, &amp;model.CreateRefundOrderRequest{
                        Amount:       order.Amount,
                        AppTransID:   order.AppTransID,
                        ZaloPayID:    order.ZalopayID,
                        Description:  order.Description,
                        RefZPTransID: order.ExtraData.RefZPTransID,
                })
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to create refund settle order", "error", err)
                        return errors.Wrap(err, "failed to create refund settle order")
                }</span>

                <span class="cov0" title="0">order = order.
                        WithStatus(model.OrderStatusPending).
                        FulfillSettleOrder(acOrder)

                // Update order status to pending
                err = uc.orderRepo.UpdateOrderProgress(ctx, model.OrderProgressUpdate{
                        OrderID:   order.ID,
                        Status:    order.Status,
                        ExtraData: order.ExtraData,
                })
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update order status", "error", err, "order_id", order.ID)
                        return errors.Wrap(err, "failed to update order status")
                }</span>

                <span class="cov0" title="0">err = uc.orderAdapter.SubmitRefundSettleOrder(ctx, order)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to submit refund settle order", "error", err, "order_id", order.ID)
                        return errors.Wrap(err, "failed to submit refund settle order")
                }</span>

                <span class="cov0" title="0">logger.Infow("msg", "Successfully submitted refund settle order", "order_id", order.ID)

                return nil</span>
        })

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Transaction failed", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "Successfully processed refund settle order",
                "order_id", order.ID,
                "app_trans_id", order.AppTransID,
                "zp_trans_id", event.SettlePaymentID)

        return nil</span>
}

func (uc *Usecase) handleRefundSettleFailed(ctx context.Context, order *model.RefundSettleOrder, event *RefundSettleResult) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        err := uc.txn.WithTx(ctx, func(tCtx context.Context) error </span><span class="cov0" title="0">{
                // Update order status to cancelled
                order.CancelOrder()

                err := uc.orderRepo.UpdateOrderProgress(tCtx, model.OrderProgressUpdate{
                        OrderID:   order.ID,
                        Status:    order.Status,
                        ZpTransID: order.ZPTransID,
                        ExtraData: order.ExtraData,
                })
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update order status", "error", err)
                        return errors.Wrap(err, "failed to update order status")
                }</span>

                <span class="cov0" title="0">err = uc.repo.UpdateRefundSettleStatusByID(tCtx, order.RefundSettleID, model.RefundSettleStatusPending)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update refund settle status", "error", err)
                        return errors.Wrap(err, "failed to update refund settle status")
                }</span>
                <span class="cov0" title="0">return nil</span>
        })

        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Transaction failed", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">return nil</span>
}

type SettleOrderUpdateRequest struct {
        AppID        int32
        AppTransID   string
        ZPTransID    int64
        OrderStatus  model.OrderStatus
        OriginStatus int
        ReasonStatus string
}

func (uc *Usecase) ProcessSettleOrderUpdate(ctx context.Context, params *SettleOrderUpdateRequest) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        order, err := uc.orderRepo.GetRefundSettleOrder(ctx, params.AppTransID, params.AppID)
        if errors.Is(err, model.ErrOrderNotFound) </span><span class="cov0" title="0">{
                logger.Warnw("msg", "Settle order not found", "error", err)
                return nil
        }</span>
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get settle order", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if order.IsFinal() </span><span class="cov0" title="0">{
                logger.Infow("msg", "Settle order is final", "order", order)
                return nil
        }</span>

        <span class="cov0" title="0">order.HandleOrderUpdate(
                params.ZPTransID,
                params.OrderStatus,
                params.ReasonStatus,
        )

        err = uc.txn.WithTx(ctx, func(tCtx context.Context) error </span><span class="cov0" title="0">{
                if err = uc.orderRepo.UpdateOrderProgress(tCtx, model.OrderProgressUpdate{
                        OrderID:   order.ID,
                        Status:    order.Status,
                        ZpTransID: order.ZPTransID,
                        ExtraData: order.ExtraData,
                }); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update settle order", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">if !order.IsSuccess() </span><span class="cov0" title="0">{
                        logger.Infow("msg", "Settle order is not success", "order", order)
                        return nil
                }</span>

                <span class="cov0" title="0">if err := uc.updatePaymentLogWithOrderInfo(tCtx, order.ID, order); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update payment log with order", "error", err, "order_id", order.ID)
                        return err
                }</span>

                <span class="cov0" title="0">if order.GetRefundSettleMode() != model.RefundSettleModeStandard </span><span class="cov0" title="0">{
                        logger.Infow("msg", "Settle order is not standard mode, not need update refund settle info", "order", order)
                        return nil
                }</span>

                <span class="cov0" title="0">settle, err := uc.repo.GetRefundSettleForUpdate(tCtx, order.RefundSettleID)
                if errors.Is(err, model.ErrRefundSettleNotFound) || settle == nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Refund settle not found")
                        return errors.New("refund settle not found")
                }</span>
                <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to get refund settle", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">if err := uc.repo.UpdateRefundSettleStatusByID(tCtx, settle.ID, model.RefundSettleStatusSettled); err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "Failed to update refund settle status", "error", err)
                        return err
                }</span>

                <span class="cov0" title="0">logger.Infow("msg", "Successfully updated refund settle status", "settle_id", settle.ID, "new_status", model.RefundSettleStatusSettled)

                return nil</span>
        })
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Transaction process settle order update has failed", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">logger.Infow("msg", "Successfully processed settle order update", "order_id", order.ID, "new_status", order.Status)

        return nil</span>
}

// updatePaymentLogWithOrder will update the early discharge log or repayment log with the order information.
// both 2 logs have serve for settlement process, so we can update them with the order information
func (uc *Usecase) updatePaymentLogWithOrderInfo(ctx context.Context, orderID int64, order *model.RefundSettleOrder) error <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        var err error
        var paymentID int64
        var orderInfo model.Order

        switch order.GetRefundSettleMode() </span>{
        case model.RefundSettleModeStandard:<span class="cov0" title="0">
                log, err := uc.paymentRepo.GetEarlyDischargeLogByOrderID(ctx, orderID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "get early discharge log by order id fail", "error", err, "order_id", orderID)
                        return err
                }</span>
                <span class="cov0" title="0">paymentID = log.ID
                orderInfo = log.WithRefundSettleOrder(order).Order</span>
        case model.RefundSettleModeExpired:<span class="cov0" title="0">
                log, err := uc.paymentRepo.GetRepaymentByOrderID(ctx, orderID)
                if err != nil </span><span class="cov0" title="0">{
                        logger.Errorw("msg", "get repayment log by order id fail", "error", err, "order_id", orderID)
                        return err
                }</span>
                <span class="cov0" title="0">paymentID = log.ID
                orderInfo = log.WithRefundSettleOrder(order).Order</span>
        default:<span class="cov0" title="0">
                logger.Errorw("msg", "unknown refund settle mode", "mode", order.GetRefundSettleMode())
                return errors.Errorf("unknown refund settle mode: %d", order.GetRefundSettleMode())</span>
        }

        <span class="cov0" title="0">if orderInfo.IsEmpty() || paymentID == 0 </span><span class="cov0" title="0">{
                logger.Errorw("msg", "order info is nil or payment id is zero", "payment_id", paymentID, "order_id", orderID)
                return errors.New("order info is nil or payment id is zero")
        }</span>

        <span class="cov0" title="0">if err = uc.paymentRepo.UpdatePaymentLogOrderIDs(ctx, paymentID, orderInfo); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "update payment log with order info fail", "error", err, "payment_id", paymentID, "order_info", orderInfo)
                return errors.Wrap(err, "failed to update payment log with order info")
        }</span>

        <span class="cov0" title="0">return nil</span>
}

func (uc *Usecase) buildRefundSettleOrder(settle *model.RefundSettle, payment *model.PurchaseOrder) (*model.RefundSettleOrder, error) <span class="cov0" title="0">{
        config, ok := uc.orderCfgs.GetConfigRefundSettle("")
        if !ok </span><span class="cov0" title="0">{
                return nil, errors.New("refund settle order config not found")
        }</span>

        <span class="cov0" title="0">zalopayID := payment.AccountInfo.ZalopayID
        partnerCode := payment.AccountInfo.PartnerCode

        result := model.NewRefundSettleOrder(
                zalopayID, partnerCode,
                settle.ZPTransID, settle.SettlementAmount,
        )
        result.AppID = config.GetAppId()
        result.AppTransID = idgen.GenAppTransID()
        result.Description = SettleOrderDescription
        result.RefundSettleID = settle.ID

        return result, nil</span>
}

func (uc *Usecase) getResourceForPreparingStandardSettle(ctx context.Context, refZPTransID int64) (*model.Account, *model.PurchaseOrder, *model.BankRoute, error) <span class="cov0" title="0">{
        logger := uc.logger.WithContext(ctx)

        purchase, err := uc.paymentRepo.GetPaymentByTransID(ctx, refZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get purchase fail", "error", err)
                return nil, nil, nil, err
        }</span>

        <span class="cov0" title="0">account, err := uc.accountAdapter.GetAccount(ctx,
                purchase.AccountInfo.ZalopayID,
                purchase.AccountInfo.PartnerCode,
        )
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get account fail", "error", err)
                return nil, nil, nil, err
        }</span>

        <span class="cov0" title="0">bankRoute, err := uc.paymentRepo.GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get bank route fail", "error", err)
                return nil, nil, nil, err
        }</span>

        <span class="cov0" title="0">return &amp;account, purchase, bankRoute, nil</span>
}
</pre>
		
		<pre class="file" id="file9" style="display: none">package refund

import (
        "context"

        "github.com/pkg/errors"
        "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
        "gitlab.zalopay.vn/fin/installment/payment-service/pkg/idgen"
)

const (
        TopupOrderDescription = "Nạp tiền vào tài khoản ZaloPay"
)

type CreateTopupRequest struct {
        Amount       int64
        ZaloPayID    int64
        RefZPTransID int64
}

func (s *Usecase) CreateTopup(ctx context.Context, req *CreateTopupRequest) (*model.RefundTopupOrder, error) <span class="cov0" title="0">{
        logger := s.logger.WithContext(ctx)

        // Get and validate settle info
        topupOrder := model.NewRefundTopupOrder(req.ZaloPayID, req.RefZPTransID, req.Amount)
        settleInfo, err := s.repo.GetRefundSettleByZPTransID(ctx, req.RefZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get settle info", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if err = s.precheckTopupCondition(ctx, settleInfo); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to precheck topup orders", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">settleInfo, err = s.ValidateAndSyncSettlementForTopup(ctx, settleInfo)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to sync latest settlement amount", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if err = settleInfo.ValidateTopupOrderAmount(topupOrder.Amount); err != nil </span><span class="cov0" title="0">{
                logger.Errorf("invalid topup order amount: %v, settleID: %d", err, settleInfo.ID)
                return nil, errors.Errorf("invalid topup order amount: %v", err)
        }</span>

        <span class="cov0" title="0">purchaseOrder, err := s.paymentRepo.GetPaymentByTransID(ctx, settleInfo.ZPTransID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get payment info", "error", err)
                return nil, err
        }</span>

        // Create td-core order
        <span class="cov0" title="0">topupOrder = topupOrder.EnrichTopupOrder(settleInfo, purchaseOrder, TopupOrderDescription)
        tdTopupOrder, err := s.orderAdapter.CreateRefundTopupOrder(ctx, &amp;model.CreateRefundOrderRequest{
                Amount:       topupOrder.Amount,
                ZaloPayID:    topupOrder.ZalopayID,
                AppTransID:   idgen.GenAppTransID(),
                Description:  topupOrder.Description,
                RefZPTransID: topupOrder.GetRefZPTransID(),
        })
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to create topup order", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">topupOrder = topupOrder.FulfillTopupOrder(tdTopupOrder)
        topupOrder, err = s.orderRepo.CreateRefundTopupOrder(ctx, topupOrder)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to save topup order", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">return topupOrder, nil</span>
}

func (s *Usecase) GetTopupOrder(ctx context.Context, appID int32, appTransID string) (*model.RefundTopupOrder, error) <span class="cov0" title="0">{
        logger := s.logger.WithContext(ctx)

        order, err := s.orderRepo.GetRefundTopupOrder(ctx, appTransID, appID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get topup orders", "error", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">return order, nil</span>
}

type TopupOrderUpdateRequest struct {
        AppID        int32
        AppTransID   string
        ZPTransID    int64
        OrderStatus  model.OrderStatus
        OriginStatus int
        ReasonStatus string
}

func (s *Usecase) ProcessTopupOrderUpdate(ctx context.Context, req *TopupOrderUpdateRequest) error <span class="cov0" title="0">{
        logger := s.logger.WithContext(ctx)

        logger.Infow("msg", "Processing topup order update", "request", req)

        order, err := s.orderRepo.GetRefundTopupOrder(ctx, req.AppTransID, req.AppID)
        if errors.Is(err, model.ErrOrderNotFound) </span><span class="cov0" title="0">{
                logger.Warnw("msg", "Topup order not found", "error", err)
                return nil
        }</span>
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get topup orders", "error", err)
                return err
        }</span>
        <span class="cov0" title="0">if order.IsFinal() </span><span class="cov0" title="0">{
                logger.Infow("msg", "Topup order is final", "order", order)
                return nil
        }</span>

        <span class="cov0" title="0">order.HandleOrderUpdate(
                req.ZPTransID,
                req.OrderStatus,
                req.ReasonStatus,
        )

        if err = s.orderRepo.UpdateOrderProgress(ctx, model.OrderProgressUpdate{
                OrderID:   order.ID,
                Status:    order.Status,
                ZpTransID: order.ZPTransID,
                ExtraData: order.ExtraData,
        }); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to update topup order", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">if !order.IsSuccess() </span><span class="cov0" title="0">{
                logger.Infow("msg", "Topup order is not success", "order", order)
                return nil
        }</span>

        <span class="cov0" title="0">ctx = context.WithoutCancel(ctx)
        go s.calcAndUpdateSettleTopups(ctx, order.RefundSettleID)

        return nil</span>
}

func (s *Usecase) calcAndUpdateSettleTopups(ctx context.Context, settleID int64) error <span class="cov0" title="0">{
        logger := s.logger.WithContext(ctx)

        orders, err := s.orderRepo.ListRefundTopupOrder(ctx, settleID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get topup orders", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">ordersCast := model.RefundTopupOrders(orders)
        topupAmount := ordersCast.GetSuccessAmount()

        if err = s.repo.UpdateRefundTopupsAmount(ctx, settleID, topupAmount); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to update topup amount", "error", err)
                return err
        }</span>

        <span class="cov0" title="0">dsCtx := context.WithoutCancel(ctx)
        go s.DispatchRefundSettleChanged(dsCtx, settleID)

        if err = s.ProcessRefundSettleInfo(ctx, settleID); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to process settle info", "error", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

func (s *Usecase) precheckTopupCondition(ctx context.Context, settle *model.RefundSettle) error <span class="cov0" title="0">{
        logger := s.logger.WithContext(ctx)

        if !settle.IsUserTopupRequired() </span><span class="cov0" title="0">{
                return errors.Errorf("Settlement is not in user topup required state, settleID: %d", settle.ID)
        }</span>

        <span class="cov0" title="0">topupOrders, err := s.orderRepo.ListRefundTopupOrder(ctx, settle.ID)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "Failed to get topup orders", "error", err)
                return errors.Errorf("Failed to get topup orders: %v", err)
        }</span>
        <span class="cov0" title="0">if len(topupOrders) == 0 </span><span class="cov0" title="0">{
                return nil
        }</span>

        <span class="cov0" title="0">topupOrdersCast := model.RefundTopupOrders(topupOrders)
        if topupOrdersCast.HasProcessingOrder() </span><span class="cov0" title="0">{
                return errors.Errorf("There is a processing topup order, settleID: %d", settle.ID)
        }</span>

        <span class="cov0" title="0">return nil</span>
}

func (s *Usecase) ValidateAndSyncSettlementForTopup(ctx context.Context, settle *model.RefundSettle) (*model.RefundSettle, error) <span class="cov0" title="0">{
        logger := s.logger.WithContext(ctx)

        earlyDischarge, err := s.installmentAdapter.GetEarlyDischarge(ctx, settle.ZPTransID, true)
        if err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "get early discharge fail", "error", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">if err = earlyDischarge.Validate(); err != nil </span><span class="cov0" title="0">{
                logger.Errorw("msg", "validate early discharge fail", "error", err)
                return nil, err
        }</span>

        // Check session availability for topups
        <span class="cov0" title="0">if !earlyDischarge.InSession </span><span class="cov0" title="0">{
                logger.Errorw("msg", "session is not available for topup", "zp_trans_id", settle.ZPTransID)
                return nil, errors.New("session not available for topup")
        }</span>

        // Reuse common logic for settlement amount syncing
        <span class="cov0" title="0">return s.syncSettlementAmountIfChanged(ctx, settle, earlyDischarge)</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
