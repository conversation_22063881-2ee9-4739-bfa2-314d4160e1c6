# Auto Refund Feature - Technical Documentation

## Overview

The Auto Refund feature provides automated refund processing capabilities for the payment service. This document details the technical implementation, data flow, and components involved in the auto refund process.

## Architecture

The Auto Refund feature is implemented as part of the payment service and interacts with several other components:

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Acquiring Core │◄────►│  Payment Service│◄────►│ Account Service │
│                 │      │                 │      │                 │
└─────────────────┘      └────────┬────────┘      └─────────────────┘
                                  │
                                  │
                         ┌────────▼────────┐
                         │                 │
                         │  Database       │
                         │                 │
                         └─────────────────┘
```

## Components

### 1. API Definitions

The API for the Auto Refund feature is defined in `api/payment/v1/refund.proto`. Key services and messages include:

- `RefundService`: Handles refund operations
- `RefundOpsService`: Provides operational endpoints for refund management
- `RefundType`: Enum that distinguishes between `AUTO` and `MANUAL` refunds

### 2. Database Schema

The feature uses two new tables and requires a modification to an existing table:

#### refund_logs

Stores individual refund transactions:

```sql
CREATE TABLE `refund_logs` (
    `id`                        bigint auto_increment NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `refund_id`                 bigint             NOT NULL,
    `amount`                    bigint             NOT NULL,
    `app_trans_id`              varchar(255)       NOT NULL,
    `app_id`                    int                NOT NULL,
    `payment_description`       varchar(1024)      NOT NULL,
    `refund_type`               enum ('AUTO', 'MANUAL')    NOT NULL,
    `status`                    enum ('INIT', 'SUCCESS', 'FAILED', 'PENDING', 'PROCESSING') NOT NULL,
    `bank_status`               varchar(20)              NOT NULL DEFAULT '',
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `process_type`              enum ('SETTLEMENT', 'REPAYMENT', 'FUNDBACK', 'MANUAL') NULL,
    `deadline_at`               datetime           NULL,
    `extra`                     json               NOT NULL,
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zp_trans_id` (`zp_trans_id`),
    KEY `idx_deadline_process_type` (`deadline_at`, `process_type`)
)
```

#### refund_settle

Manages the settlement process for refunds:

```sql
CREATE TABLE `refund_settle` (
    `id`                        bigint auto_increment NOT NULL,
    `status`                    enum ('INIT', 'PENDING', 'PROCESSING', 'SETTLED', 'COMPLETED') NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `net_refund_amount`         bigint             NOT NULL,
    `total_refund_amount`       bigint             NOT NULL,
    `settlement_amount`         bigint             NOT NULL,
    `user_topup_amount`         bigint             NOT NULL DEFAULT 0,
    `user_payback_amount`       bigint             NOT NULL DEFAULT 0,
    `event_version`             int                NOT NULL DEFAULT 0,
    `metadata`                  json               NOT NULL DEFAULT '{}',
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_settle` (`zp_trans_id`)
)
```

#### payment_logs (modified)

The existing `payment_logs` table is modified to add an `order_id` column:

```sql
ALTER TABLE payment_logs ADD COLUMN order_id bigint AFTER id;
```

This modification allows payment logs to be linked directly to orders, which is essential for the refund process.

### 3. Business Logic

The core business logic is implemented in `payment/internal/usecase/refund/`. Key functions include:

- `Refund`: Processes a refund request
- `RefundQuery`: Queries the status of a refund
- `ProcessCompletion`: Finalizes a refund transaction

### 4. Event Processing

The Auto Refund feature uses Kafka for event-driven processing:

- `ac_refund_info_updated`: Consumes refund information updates from Acquiring Core
- `refund_settle_process`: Handles refund settlement processing
- `refund_settle_request`: Processes refund settlement requests
- `refund_settle_response`: Processes responses from settlement requests

### 5. Scheduled Jobs

Several scheduled workflows manage the refund lifecycle:

- `ReconcileRefundSettle`: Reconciles refund settlements
- `PollingEarlyDischarge`: Polls for early discharge events
- `FundbackAfterSettlement`: Processes fund returns after settlement
- `RefundExpiredProcess`: Handles expired refunds
- `PollingExpiredRefundRepay`: Polls for expired refund repayments

## Data Flow

### Auto Refund Process

1. A refund is initiated either automatically or manually
2. The system creates a record in the `refund_logs` table with `refund_type` set to `AUTO` or `MANUAL`
3. For auto refunds, the system processes the refund without user intervention
4. The refund status is updated in the database as it progresses
5. Settlement processing occurs through Kafka events
6. The final status is updated in the database

### Settlement Process

1. A refund settlement is initiated
2. A record is created in the `refund_settle` table
3. The settlement request is published to Kafka
4. The settlement processor consumes the event and processes it
5. The result is published back to Kafka
6. The settlement status is updated in the database

## Configuration

The Auto Refund feature is configured through several configuration files:

### Environment Configuration (config/prod.yaml)

Contains environment-specific settings:

- Kafka broker addresses
- Topic names
- Consumer group IDs
- Worker counts

### Order Configurations (configs/conf.go)

Provides helper methods for accessing order configurations:

- `GetConfigRefundTopup`: Gets configuration for refund topups
- `GetConfigRefundSettle`: Gets configuration for refund settlements
- `GetConfigRefundFundback`: Gets configuration for refund fundbacks

## Integration Points

### Acquiring Core Service

- Provides payment transaction information
- Receives refund requests
- Updates refund statuses

### Account Service

- Manages user account information
- Handles fund transfers for refunds

### Installment Service

- Provides installment plan information
- Receives updates about refunds affecting installment plans

## Error Handling

The Auto Refund feature implements several error handling mechanisms:

1. **Retry Logic**: Failed operations are retried with exponential backoff
2. **Dead Letter Queues**: Messages that cannot be processed are sent to DLQs
3. **Error Logging**: Detailed error information is logged for troubleshooting
4. **Status Tracking**: Refund status is tracked in the database for monitoring

## Monitoring

The following metrics should be monitored:

1. **Refund Processing Rate**: Number of refunds processed per minute
2. **Success Rate**: Percentage of successful refunds
3. **Processing Time**: Time taken to process refunds
4. **Error Rate**: Number of errors encountered during processing
5. **Queue Depth**: Number of pending refund requests

## Security Considerations

1. **Authentication**: All API calls require proper authentication
2. **Authorization**: Access to refund operations is restricted to authorized users
3. **Data Encryption**: Sensitive data is encrypted in transit and at rest
4. **Audit Logging**: All refund operations are logged for audit purposes

## Performance Considerations

1. **Database Indexing**: Key fields are indexed for optimal query performance
2. **Connection Pooling**: Database connections are pooled for efficient resource usage
3. **Asynchronous Processing**: Long-running operations are processed asynchronously
4. **Batch Processing**: Bulk operations are processed in batches for efficiency

## Testing Strategy

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test the complete refund flow
4. **Performance Tests**: Test system performance under load
5. **Chaos Tests**: Test system resilience to failures
