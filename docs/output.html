<h1 id="auto-refund-feature-deployment-checklist">Auto Refund Feature
Deployment Checklist</h1>
<h2 id="overview">Overview</h2>
<p>This document provides a checklist for deploying the Auto Refund
feature in the payment service. The deployment involves:</p>
<ol type="1">
<li><strong>Database Changes</strong>:
<ul>
<li>Creating new tables: <code>refund_logs</code> and
<code>refund_settle</code></li>
<li>Adding <code>order_id</code> column to the <code>payment_logs</code>
table</li>
<li>No changes required to the <code>order</code> table structure</li>
</ul></li>
<li><strong>Kafka Configuration</strong>:
<ul>
<li>Setting up topics and consumer groups for refund processing</li>
</ul></li>
<li><strong>External Service Connections</strong>:
<ul>
<li>Updating Acquiring Core and Account Service adapters</li>
</ul></li>
<li><strong>Temporal Workflow Configuration</strong>:
<ul>
<li>Configuring workflows for refund settlement and processing</li>
</ul></li>
</ol>
<h2 id="pre-deployment-verification">Pre-Deployment Verification</h2>
<h3 id="environment-requirements">Environment Requirements</h3>
<ul>
<li>Go version 1.22.5 or later</li>
<li>gRPC-Go v1.64.0 or later</li>
<li>Protocol Buffers v5.28.3 or later</li>
</ul>
<h2 id="infrastructure-deployment-checklist">Infrastructure Deployment
Checklist</h2>
<h3 id="docker-configuration">1. Docker Configuration</h3>
<ul>
<li>Update Dockerfile ENTRYPOINT from <code>["/app", "start"]</code> to
<code>["/app"]</code></li>
<li>Verify Docker image builds successfully with the new ENTRYPOINT</li>
<li>Update Kubernetes deployment manifests if they reference the “start”
command</li>
</ul>
<h3 id="database-setup">2. Database Setup</h3>
<h4 id="database-tables">Database Tables</h4>
<p><strong>Create new refund tables:</strong></p>
<div class="sourceCode" id="cb1"><pre
class="sourceCode sql"><code class="sourceCode sql"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="kw">CREATE</span> <span class="kw">TABLE</span> <span class="cf">IF</span> <span class="kw">NOT</span> <span class="kw">EXISTS</span> `refund_logs` (</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a>    `id`                        bigint auto_increment <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a>    `zp_trans_id`               bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>    `refund_id`                 bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a>    `amount`                    bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a>    `app_trans_id`              <span class="dt">varchar</span>(<span class="dv">255</span>)       <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a>    `app_id`                    <span class="dt">int</span>                <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a>    `payment_description`       <span class="dt">varchar</span>(<span class="dv">1024</span>)      <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a>    `refund_type`               enum (<span class="st">&#39;AUTO&#39;</span>, <span class="st">&#39;MANUAL&#39;</span>)    <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a>    `status`                    enum (<span class="st">&#39;INIT&#39;</span>, <span class="st">&#39;SUCCESS&#39;</span>, <span class="st">&#39;FAILED&#39;</span>, <span class="st">&#39;PENDING&#39;</span>, <span class="st">&#39;PROCESSING&#39;</span>) <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a>    `bank_status`               <span class="dt">varchar</span>(<span class="dv">20</span>)              <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="st">&#39;&#39;</span>,</span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a>    `error_code`                <span class="dt">varchar</span>(<span class="dv">255</span>)             <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="st">&#39;&#39;</span>,</span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a>    `error_message`             <span class="dt">varchar</span>(<span class="dv">255</span>)             <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="st">&#39;&#39;</span>,</span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a>    `process_type`              enum (<span class="st">&#39;SETTLEMENT&#39;</span>, <span class="st">&#39;REPAYMENT&#39;</span>, <span class="st">&#39;FUNDBACK&#39;</span>, <span class="st">&#39;MANUAL&#39;</span>) <span class="kw">NULL</span>,</span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a>    `deadline_at`               datetime           <span class="kw">NULL</span>,</span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a>    `extra`                     json               <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a>    `created_at`                datetime           <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="fu">CURRENT_TIMESTAMP</span>,</span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a>    `updated_at`                datetime           <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="fu">CURRENT_TIMESTAMP</span> <span class="kw">ON</span> <span class="kw">UPDATE</span> <span class="fu">CURRENT_TIMESTAMP</span>,</span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a>    <span class="kw">PRIMARY</span> <span class="kw">KEY</span> (`id`),</span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a>    <span class="kw">KEY</span> `idx_zp_trans_id` (`zp_trans_id`),</span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>    <span class="kw">KEY</span> `idx_deadline_process_type` (`deadline_at`, `process_type`)</span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>)</span></code></pre></div>
<div class="sourceCode" id="cb2"><pre
class="sourceCode sql"><code class="sourceCode sql"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="kw">CREATE</span> <span class="kw">TABLE</span> <span class="cf">IF</span> <span class="kw">NOT</span> <span class="kw">EXISTS</span> `refund_settle` (</span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a>    `id`                        bigint auto_increment <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a>    `status`                    enum (<span class="st">&#39;INIT&#39;</span>, <span class="st">&#39;PENDING&#39;</span>, <span class="st">&#39;PROCESSING&#39;</span>, <span class="st">&#39;SETTLED&#39;</span>, <span class="st">&#39;COMPLETED&#39;</span>) <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a>    `zp_trans_id`               bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>    `net_refund_amount`         bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>    `total_refund_amount`       bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a>    `settlement_amount`         bigint             <span class="kw">NOT</span> <span class="kw">NULL</span>,</span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a>    `user_topup_amount`         bigint             <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="dv">0</span>,</span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a>    `user_payback_amount`       bigint             <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="dv">0</span>,</span>
<span id="cb2-10"><a href="#cb2-10" aria-hidden="true" tabindex="-1"></a>    `event_version`             <span class="dt">int</span>                <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="dv">0</span>,</span>
<span id="cb2-11"><a href="#cb2-11" aria-hidden="true" tabindex="-1"></a>    `metadata`                  json               <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="st">&#39;{}&#39;</span>,</span>
<span id="cb2-12"><a href="#cb2-12" aria-hidden="true" tabindex="-1"></a>    `created_at`                datetime           <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="fu">CURRENT_TIMESTAMP</span>,</span>
<span id="cb2-13"><a href="#cb2-13" aria-hidden="true" tabindex="-1"></a>    `updated_at`                datetime           <span class="kw">NOT</span> <span class="kw">NULL</span> <span class="kw">DEFAULT</span> <span class="fu">CURRENT_TIMESTAMP</span> <span class="kw">ON</span> <span class="kw">UPDATE</span> <span class="fu">CURRENT_TIMESTAMP</span>,</span>
<span id="cb2-14"><a href="#cb2-14" aria-hidden="true" tabindex="-1"></a>    <span class="kw">PRIMARY</span> <span class="kw">KEY</span> (`id`),</span>
<span id="cb2-15"><a href="#cb2-15" aria-hidden="true" tabindex="-1"></a>    <span class="kw">UNIQUE</span> <span class="kw">KEY</span> `uk_refund_settle` (`zp_trans_id`)</span>
<span id="cb2-16"><a href="#cb2-16" aria-hidden="true" tabindex="-1"></a>)</span></code></pre></div>
<h4 id="update-existing-tables">Update Existing Tables</h4>
<p><strong>Update payment_logs table</strong></p>
<p>Add new field to the <code>payment_logs</code> table:</p>
<div class="sourceCode" id="cb3"><pre
class="sourceCode sql"><code class="sourceCode sql"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="kw">ALTER</span> <span class="kw">TABLE</span> payment_logs <span class="kw">ADD</span> <span class="kw">COLUMN</span> order_id bigint <span class="kw">AFTER</span> <span class="kw">id</span>;</span></code></pre></div>
<p>Update the CreatePaymentLog SQL query to include the new fields:</p>
<div class="sourceCode" id="cb4"><pre
class="sourceCode sql"><code class="sourceCode sql"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co">-- name: CreatePaymentLog :execresult</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> payment_logs(zp_trans_id,</span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a>                       order_id,</span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>                       system_id,</span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>                       trans_type,</span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a>                       partner_req_id,</span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a>                       partner_trans_id,</span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a>                       <span class="op">..</span>.</span></code></pre></div>
<p><strong>Update order table</strong></p>
<p>No schema changes required for the <code>order</code> table as it
already has the necessary fields: - <code>type</code> enum with ‘refund’
value - <code>refund_id</code> field for linking to refund records -
<code>settle_id</code> field for settlement tracking</p>
<h3 id="kafka-configuration">3. Kafka Configuration</h3>
<h4 id="kafka-topics">Kafka Topics</h4>
<p>Create and configure the following Kafka topics: -
<code>ZPReportTransLog</code> - For receiving refund information from
Acquiring Core -
<code>prod.fin.installment.payment.refund.settle_process</code> - For
refund settlement processing</p>
<h4 id="consumer-groups">Consumer Groups</h4>
<p>Set up the following consumer groups: -
<code>prod.fin.installment.payment.ac_refund.group</code> - For
consuming refund information updates -
<code>prod.fin.installment.payment.refund.settle_process.group</code> -
For processing refund settlements -
<code>prod.fin.installment.payment.refund.settle_request.group</code> -
For handling refund settlement requests -
<code>prod.fin.installment.payment.refund.settle_response.group</code> -
For processing refund settlement responses</p>
<h4 id="kafka-configuration-settings">Kafka Configuration Settings</h4>
<ul>
<li>Configure each consumer with 2 workers for parallel processing</li>
<li>Set appropriate message retention periods (recommended: 7 days)</li>
<li>Configure error handling and dead letter queues</li>
</ul>
<h3 id="external-service-adapters">4. External Service Adapters</h3>
<h4 id="acquiring-core-service">Acquiring Core Service</h4>
<p>Update Acquiring Core adapter to support new features: - New
OrderStatus values: <code>AUTHORIZED</code> and <code>CANCELED</code> -
New PaymentStatus value: <code>PAYMENT_AUTHORIZED</code> - New
CaptureMethod enum: <code>CAPTURE_METHOD_AUTO</code> and
<code>CAPTURE_METHOD_MANUAL</code> - New message types:
<code>CaptureRequest</code> and <code>CaptureResponse</code></p>
<p>Connection settings: - Host:
<code>{{ fin.installment.adapters.acquiring_core_service.address }}</code>
- Timeout: 30s - Secured:
<code>{{ fin.installment.adapters.acquiring_core_service.secured }}</code></p>
<h4 id="account-service">Account Service</h4>
<ul>
<li>Update Account Service adapter to use gRPC-Go v1.64.0 or later</li>
<li>Update gRPC client configurations to use
<code>grpc.StaticMethod()</code> option</li>
</ul>
<p>Connection settings: - Host:
<code>{{ fin.installment.adapters.account_service.address }}</code> -
Timeout: 30s - Secured:
<code>{{ fin.installment.adapters.account_service.secured }}</code></p>
<h3 id="configuration-files">5. Configuration Files</h3>
<p>Update environment configuration in
<code>config/prod.yaml</code>:</p>
<div class="sourceCode" id="cb5"><pre
class="sourceCode yaml"><code class="sourceCode yaml"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="fu">payment</span><span class="kw">:</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">server</span><span class="kw">:</span></span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">consumer</span><span class="kw">:</span></span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">ac_refund_info_updated</span><span class="kw">:</span></span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">brokers</span><span class="kw">:</span><span class="at"> </span><span class="kw">{</span><span class="at">{ fin.installment.payment.ac_kafka.refunds.brokers </span><span class="kw">}</span><span class="at">}</span></span>
<span id="cb5-6"><a href="#cb5-6" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">topic</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;ZPReportTransLog&quot;</span></span>
<span id="cb5-7"><a href="#cb5-7" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">group_id</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.ac_refund.group&quot;</span></span>
<span id="cb5-8"><a href="#cb5-8" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">num_workers</span><span class="kw">:</span><span class="at"> </span><span class="dv">2</span></span>
<span id="cb5-9"><a href="#cb5-9" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">refund_settle_process</span><span class="kw">:</span></span>
<span id="cb5-10"><a href="#cb5-10" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">brokers</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}&quot;</span></span>
<span id="cb5-11"><a href="#cb5-11" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">topic</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_process&quot;</span></span>
<span id="cb5-12"><a href="#cb5-12" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">group_id</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_process.group&quot;</span></span>
<span id="cb5-13"><a href="#cb5-13" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">num_workers</span><span class="kw">:</span><span class="at"> </span><span class="dv">2</span></span>
<span id="cb5-14"><a href="#cb5-14" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">refund_settle_request</span><span class="kw">:</span></span>
<span id="cb5-15"><a href="#cb5-15" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">brokers</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}&quot;</span></span>
<span id="cb5-16"><a href="#cb5-16" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">topic</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_process&quot;</span></span>
<span id="cb5-17"><a href="#cb5-17" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">group_id</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_request.group&quot;</span></span>
<span id="cb5-18"><a href="#cb5-18" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">num_workers</span><span class="kw">:</span><span class="at"> </span><span class="dv">2</span></span>
<span id="cb5-19"><a href="#cb5-19" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">refund_settle_response</span><span class="kw">:</span></span>
<span id="cb5-20"><a href="#cb5-20" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">brokers</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}&quot;</span></span>
<span id="cb5-21"><a href="#cb5-21" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">topic</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_process&quot;</span></span>
<span id="cb5-22"><a href="#cb5-22" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">group_id</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_response.group&quot;</span></span>
<span id="cb5-23"><a href="#cb5-23" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">num_workers</span><span class="kw">:</span><span class="at"> </span><span class="dv">2</span></span>
<span id="cb5-24"><a href="#cb5-24" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">publishers</span><span class="kw">:</span></span>
<span id="cb5-25"><a href="#cb5-25" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_settle_process</span><span class="kw">:</span></span>
<span id="cb5-26"><a href="#cb5-26" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">brokers</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}&quot;</span></span>
<span id="cb5-27"><a href="#cb5-27" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">topic</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;prod.fin.installment.payment.refund.settle_process&quot;</span></span>
<span id="cb5-28"><a href="#cb5-28" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">schedulers</span><span class="kw">:</span></span>
<span id="cb5-29"><a href="#cb5-29" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_settle_recon</span><span class="kw">:</span></span>
<span id="cb5-30"><a href="#cb5-30" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">queue_name</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;installment.refund&quot;</span></span>
<span id="cb5-31"><a href="#cb5-31" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">workflow_type</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;ReconcileRefundSettle&quot;</span></span>
<span id="cb5-32"><a href="#cb5-32" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_discharge_poll</span><span class="kw">:</span></span>
<span id="cb5-33"><a href="#cb5-33" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">queue_name</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;installment.refund&quot;</span></span>
<span id="cb5-34"><a href="#cb5-34" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">workflow_type</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;PollingEarlyDischarge&quot;</span></span>
<span id="cb5-35"><a href="#cb5-35" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_fundback_process</span><span class="kw">:</span></span>
<span id="cb5-36"><a href="#cb5-36" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">queue_name</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;installment.refund&quot;</span></span>
<span id="cb5-37"><a href="#cb5-37" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">workflow_type</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;FundbackAfterSettlement&quot;</span></span>
<span id="cb5-38"><a href="#cb5-38" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_expired_process</span><span class="kw">:</span></span>
<span id="cb5-39"><a href="#cb5-39" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">queue_name</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;installment.refund&quot;</span></span>
<span id="cb5-40"><a href="#cb5-40" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">workflow_type</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;RefundExpiredProcess&quot;</span></span>
<span id="cb5-41"><a href="#cb5-41" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_expired_repay_poll</span><span class="kw">:</span></span>
<span id="cb5-42"><a href="#cb5-42" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">queue_name</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;installment.refund&quot;</span></span>
<span id="cb5-43"><a href="#cb5-43" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="fu">workflow_type</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;PollingExpiredRefundRepay&quot;</span></span>
<span id="cb5-44"><a href="#cb5-44" aria-hidden="true" tabindex="-1"></a><span class="at">  </span><span class="fu">order_configs</span><span class="kw">:</span></span>
<span id="cb5-45"><a href="#cb5-45" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_topup</span><span class="kw">:</span></span>
<span id="cb5-46"><a href="#cb5-46" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="kw">-</span><span class="at"> </span><span class="fu">app_id</span><span class="kw">:</span><span class="at"> </span><span class="dv">4142</span></span>
<span id="cb5-47"><a href="#cb5-47" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">mac_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_topup.mac_key }}&quot;</span></span>
<span id="cb5-48"><a href="#cb5-48" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">callback_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_topup.callback_key }}&quot;</span></span>
<span id="cb5-49"><a href="#cb5-49" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">product_code</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;TU010&quot;</span></span>
<span id="cb5-50"><a href="#cb5-50" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_settle</span><span class="kw">:</span></span>
<span id="cb5-51"><a href="#cb5-51" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="kw">-</span><span class="at"> </span><span class="fu">app_id</span><span class="kw">:</span><span class="at"> </span><span class="dv">4143</span></span>
<span id="cb5-52"><a href="#cb5-52" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">mac_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_settle.mac_key }}&quot;</span></span>
<span id="cb5-53"><a href="#cb5-53" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">callback_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_settle.callback_key }}&quot;</span></span>
<span id="cb5-54"><a href="#cb5-54" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">merchant_id</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}&quot;</span></span>
<span id="cb5-55"><a href="#cb5-55" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="kw">-</span><span class="at"> </span><span class="fu">app_id</span><span class="kw">:</span><span class="at"> </span><span class="dv">4143</span></span>
<span id="cb5-56"><a href="#cb5-56" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">mac_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_settle.mac_key }}&quot;</span></span>
<span id="cb5-57"><a href="#cb5-57" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">callback_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_settle.callback_key }}&quot;</span></span>
<span id="cb5-58"><a href="#cb5-58" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">merchant_id</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}&quot;</span></span>
<span id="cb5-59"><a href="#cb5-59" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">partner_code</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;CIMB&quot;</span></span>
<span id="cb5-60"><a href="#cb5-60" aria-hidden="true" tabindex="-1"></a><span class="at">    </span><span class="fu">refund_fundback</span><span class="kw">:</span></span>
<span id="cb5-61"><a href="#cb5-61" aria-hidden="true" tabindex="-1"></a><span class="at">      </span><span class="kw">-</span><span class="at"> </span><span class="fu">app_id</span><span class="kw">:</span><span class="at"> </span><span class="dv">4142</span></span>
<span id="cb5-62"><a href="#cb5-62" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">mac_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_fundback.mac_key }}&quot;</span></span>
<span id="cb5-63"><a href="#cb5-63" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">callback_key</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;{{ fin.installment.payment.order_configs.refund_fundback.callback_key }}&quot;</span></span>
<span id="cb5-64"><a href="#cb5-64" aria-hidden="true" tabindex="-1"></a><span class="at">        </span><span class="fu">product_code</span><span class="kw">:</span><span class="at"> </span><span class="st">&quot;TF028&quot;</span></span></code></pre></div>
<h3 id="temporal-workflow-configuration">6. Temporal Workflow
Configuration</h3>
<h4 id="temporal-connection-settings">Temporal Connection Settings</h4>
<p>Configure Temporal connection: - Address:
<code>{{ fin.installment.adapters.temporal.address }}</code> -
Namespace:
<code>{{ fin.installment.adapters.temporal.namespace }}</code> - Enable
SSL: true</p>
<h4 id="refund-workflows">Refund Workflows</h4>
<p>Register and configure the following workflows: -
<code>ReconcileRefundSettle</code> - Reconciles refund settlements -
<code>PollingEarlyDischarge</code> - Polls for early discharge events -
<code>FundbackAfterSettlement</code> - Processes fund returns after
settlement - <code>RefundExpiredProcess</code> - Handles expired refunds
- <code>PollingExpiredRefundRepay</code> - Polls for expired refund
repayments</p>
<p>Configure workflow queue: - Queue name:
<code>installment.refund</code></p>
<h2 id="deployment-steps">Deployment Steps</h2>
<h3 id="database-migration">1. Database Migration</h3>
<p>Apply the following database changes: - Create new
<code>refund_logs</code> table for tracking refunds - Create new
<code>refund_settle</code> table for managing refund settlements - Add
<code>order_id</code> column to the <code>payment_logs</code> table -
Update SQL queries to include new fields</p>
<p>Run database migration scripts:</p>
<div class="sourceCode" id="cb6"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Create new refund tables</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="ex">mysql</span> <span class="at">-h</span> <span class="pp">[</span><span class="ss">HOST</span><span class="pp">]</span> <span class="at">-u</span> <span class="pp">[</span><span class="ss">USERNAME</span><span class="pp">]</span> <span class="at">-p</span> <span class="pp">[</span><span class="ss">DATABASE</span><span class="pp">]</span> <span class="op">&lt;</span> payment/resources/sql/refund.sql</span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a><span class="co"># Add order_id column to payment_logs table</span></span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a><span class="ex">mysql</span> <span class="at">-h</span> <span class="pp">[</span><span class="ss">HOST</span><span class="pp">]</span> <span class="at">-u</span> <span class="pp">[</span><span class="ss">USERNAME</span><span class="pp">]</span> <span class="at">-p</span> <span class="pp">[</span><span class="ss">DATABASE</span><span class="pp">]</span> <span class="at">-e</span> <span class="st">&quot;ALTER TABLE payment_logs ADD COLUMN order_id bigint AFTER id;&quot;</span></span></code></pre></div>
<p>Verify migrations completed successfully:</p>
<div class="sourceCode" id="cb7"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Check refund_logs table structure</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="ex">mysql</span> <span class="at">-h</span> <span class="pp">[</span><span class="ss">HOST</span><span class="pp">]</span> <span class="at">-u</span> <span class="pp">[</span><span class="ss">USERNAME</span><span class="pp">]</span> <span class="at">-p</span> <span class="pp">[</span><span class="ss">DATABASE</span><span class="pp">]</span> <span class="at">-e</span> <span class="st">&quot;DESCRIBE refund_logs;&quot;</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a><span class="co"># Check refund_settle table structure</span></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a><span class="ex">mysql</span> <span class="at">-h</span> <span class="pp">[</span><span class="ss">HOST</span><span class="pp">]</span> <span class="at">-u</span> <span class="pp">[</span><span class="ss">USERNAME</span><span class="pp">]</span> <span class="at">-p</span> <span class="pp">[</span><span class="ss">DATABASE</span><span class="pp">]</span> <span class="at">-e</span> <span class="st">&quot;DESCRIBE refund_settle;&quot;</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a><span class="co"># Verify payment_logs has order_id column</span></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a><span class="ex">mysql</span> <span class="at">-h</span> <span class="pp">[</span><span class="ss">HOST</span><span class="pp">]</span> <span class="at">-u</span> <span class="pp">[</span><span class="ss">USERNAME</span><span class="pp">]</span> <span class="at">-p</span> <span class="pp">[</span><span class="ss">DATABASE</span><span class="pp">]</span> <span class="at">-e</span> <span class="st">&quot;DESCRIBE payment_logs;&quot;</span></span></code></pre></div>
<h3 id="service-deployment">2. Service Deployment</h3>
<p>Build the service with updated dependencies:</p>
<div class="sourceCode" id="cb8"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="ex">go</span> build <span class="at">-o</span> app</span></code></pre></div>
<p>Update Docker image:</p>
<div class="sourceCode" id="cb9"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="ex">docker</span> build <span class="at">-t</span> payment-service:latest .</span></code></pre></div>
<p>Deploy the service to the target environment:</p>
<div class="sourceCode" id="cb10"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="ex">kubectl</span> apply <span class="at">-f</span> k8s/payment-service.yaml</span></code></pre></div>
<p>Verify service health checks are passing.</p>
<h3 id="post-deployment-verification">3. Post-Deployment
Verification</h3>
<h4 id="kafka-verification">Kafka Verification</h4>
<p>Verify Kafka consumers are running and processing messages:</p>
<div class="sourceCode" id="cb11"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Check consumer group status</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="ex">kafka-consumer-groups.sh</span> <span class="at">--bootstrap-server</span> <span class="pp">[</span><span class="ss">BROKER</span><span class="pp">]</span> <span class="at">--describe</span> <span class="at">--group</span> prod.fin.installment.payment.ac_refund.group</span></code></pre></div>
<h4 id="temporal-verification">Temporal Verification</h4>
<p>Verify Temporal workflows are registered and running:</p>
<div class="sourceCode" id="cb12"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="ex">tctl</span> <span class="at">--namespace</span> <span class="pp">[</span><span class="ss">NAMESPACE</span><span class="pp">]</span> workflow list</span></code></pre></div>
<h4 id="end-to-end-testing">End-to-End Testing</h4>
<p>Test the refund flow with the following steps: 1. Create a test
refund in a controlled environment 2. Verify the refund is recorded in
the <code>refund_logs</code> table 3. Verify the settlement process is
triggered 4. Verify the refund is completed successfully</p>
<h2 id="monitoring-setup">Monitoring Setup</h2>
<h3 id="metrics">Metrics</h3>
<p>Configure metrics for refund processing: - Success rate - Processing
time - Error rate - Queue depth</p>
<h3 id="logging">Logging</h3>
<ul>
<li>Configure logging for refund-related events</li>
<li>Set up log aggregation for refund processing</li>
</ul>
<h3 id="alerting">Alerting</h3>
<ul>
<li>Configure alerts for refund processing issues</li>
<li>Set up notification channels for alerts</li>
</ul>
<h2 id="rollback-plan">Rollback Plan</h2>
<h3 id="service-rollback">Service Rollback</h3>
<p>If issues are encountered:</p>
<div class="sourceCode" id="cb13"><pre
class="sourceCode bash"><code class="sourceCode bash"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="ex">kubectl</span> rollout undo deployment/payment-service</span></code></pre></div>
<h3 id="database-rollback">Database Rollback</h3>
<p>If database issues are encountered: - Restore database from backup if
necessary - Apply rollback migrations:
<code>sql   ALTER TABLE payment_logs DROP COLUMN order_id;   DROP TABLE IF EXISTS refund_settle;   DROP TABLE IF EXISTS refund_logs;</code></p>
<h3 id="configuration-rollback">Configuration Rollback</h3>
<ul>
<li>Revert configuration changes if necessary</li>
</ul>
<h2 id="support-information">Support Information</h2>
<h3 id="contact-information">Contact Information</h3>
<ul>
<li><strong>Primary Contact</strong>: DevOps Team</li>
<li><strong>Secondary Contact</strong>: Payment Service Team</li>
<li><strong>Slack Channel</strong>: #fin-installment-support</li>
</ul>
