# Auto Refund Feature Deployment Guide

## Overview

The Auto Refund feature allows for automatic refund processing in the payment service. This document outlines the deployment steps and configuration required to enable this feature in production.

## Components

The Auto Refund feature consists of the following components:

1. **API Definitions**: Located in `api/payment/v1/refund.proto`
2. **Business Logic**: Located in `payment/internal/usecase/refund/`
3. **Database Schema**: Located in `payment/resources/sql/refund.sql`
4. **Configuration**: Located in `config/` and `configs/`

## Deployment Steps

### 1. Database Migration

Execute the SQL scripts to create or update the necessary database tables:

```sql
-- From payment/resources/sql/refund.sql
CREATE TABLE IF NOT EXISTS `refund_logs` (
    `id`                        bigint auto_increment NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `refund_id`                 bigint             NOT NULL,
    `amount`                    bigint             NOT NULL,
    `app_trans_id`              varchar(255)       NOT NULL,
    `app_id`                    int                NOT NULL,
    `payment_description`       varchar(1024)      NOT NULL,
    `refund_type`               enum ('AUTO', 'MANUAL')    NOT NULL,
    `status`                    enum ('INIT', 'SUCCESS', 'FAILED', 'PENDING', 'PROCESSING') NOT NULL,
    `bank_status`               varchar(20)              NOT NULL DEFAULT '',
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `process_type`              enum ('SETTLEMENT', 'REPAYMENT', 'FUNDBACK', 'MANUAL') NULL,
    `deadline_at`               datetime           NULL,
    `extra`                     json               NOT NULL,
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zp_trans_id` (`zp_trans_id`),
    KEY `idx_deadline_process_type` (`deadline_at`, `process_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `refund_settle` (
    `id`                        bigint auto_increment NOT NULL,
    `status`                    enum ('INIT', 'PENDING', 'PROCESSING', 'SETTLED', 'COMPLETED') NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `net_refund_amount`         bigint             NOT NULL,
    `total_refund_amount`       bigint             NOT NULL,
    `settlement_amount`         bigint             NOT NULL,
    `user_topup_amount`         bigint             NOT NULL DEFAULT 0,
    `user_payback_amount`       bigint             NOT NULL DEFAULT 0,
    `event_version`             int                NOT NULL DEFAULT 0,
    `metadata`                  json               NOT NULL DEFAULT '{}',
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_settle` (`zp_trans_id`)
)ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
```

### 2. Configuration Updates

Update the configuration files with the necessary settings for the Auto Refund feature:

#### Environment Configuration (config/prod.yaml)

Ensure the following configurations are present in the production configuration file:

```yaml
payment:
  server:
    consumer:
      ac_refund_info_updated:
        brokers: {{ fin.installment.payment.ac_kafka.refunds.brokers }}
        topic: "ZPReportTransLog"
        group_id: "prod.fin.installment.payment.ac_refund.group"
        num_workers: 2
      refund_settle_process:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_process.group"
        num_workers: 2
      refund_settle_request:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_request.group"
        num_workers: 2
      refund_settle_response:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_response.group"
        num_workers: 2
  publishers:
    refund_settle_process:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "prod.fin.installment.payment.refund.settle_process"
  schedulers:
    refund_settle_recon:
      queue_name: "installment.refund"
      workflow_type: "ReconcileRefundSettle"
    refund_discharge_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingEarlyDischarge"
    refund_fundback_process:
      queue_name: "installment.refund"
      workflow_type: "FundbackAfterSettlement"
    refund_expired_process:
      queue_name: "installment.refund"
      workflow_type: "RefundExpiredProcess"
    refund_expired_repay_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingExpiredRefundRepay"
  order_configs:
    refund_topup:
      - app_id: 4142
        mac_key: "{{ fin.installment.payment.order_configs.refund_topup.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_topup.callback_key }}"
        product_code: "TU010"
    refund_settle:
      - app_id: 4143
        mac_key: "{{ fin.installment.payment.order_configs.refund_settle.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_settle.callback_key }}"
        merchant_id: "{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}"
      - app_id: 4143
        mac_key: "{{ fin.installment.payment.order_configs.refund_settle.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_settle.callback_key }}"
        merchant_id: "{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}"
        partner_code: "CIMB"
    refund_fundback:
      - app_id: 4142
        mac_key: "{{ fin.installment.payment.order_configs.refund_fundback.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_fundback.callback_key }}"
        product_code: "TF028"
```

### 3. Service Deployment

1. Build the service:
   ```bash
   go build -o app
   ```

2. Update the Docker image:
   ```bash
   docker build -t payment-service:latest .
   ```

3. Deploy the service using Kubernetes:
   ```bash
   kubectl apply -f k8s/payment-service.yaml
   ```

### 4. Kafka Topics Setup

Ensure the following Kafka topics are created:

1. `prod.fin.installment.payment.refund.settle_process` - For refund settlement processing
2. `ZPReportTransLog` - For acquiring core refund information updates

### 5. Monitoring Setup

1. Set up alerts for the following metrics:
   - Refund processing success rate
   - Refund processing latency
   - Error rates in refund processing

2. Configure logging to capture important events:
   - Refund initiation
   - Refund status changes
   - Settlement processing

### 6. Rollback Plan

In case of issues during deployment, follow these steps to rollback:

1. Revert to the previous version of the service:
   ```bash
   kubectl rollout undo deployment/payment-service
   ```

2. If database migrations have been applied, restore from the latest backup.

## Post-Deployment Verification

After deployment, verify the following:

1. The service is running and healthy:
   ```bash
   kubectl get pods -l app=payment-service
   ```

2. The API endpoints are accessible:
   ```bash
   grpcurl -plaintext <service-address>:9090 api.payment.v1.RefundService/PreCheck
   ```

3. Test a sample refund flow to ensure the entire process works correctly.

## Support Information

For any issues during or after deployment, contact:

- **Primary Contact**: DevOps Team
- **Secondary Contact**: Payment Service Team
- **Slack Channel**: #fin-installment-support
