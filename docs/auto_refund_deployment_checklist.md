# Auto Refund Feature Deployment Checklist

## Overview

This document provides a checklist for deploying the Auto Refund feature in the payment service. The deployment involves:

1. **Database Changes**:

   - Creating new tables: `refund_logs` and `refund_settle`
   - Adding `order_id` column to the `payment_logs` table
   - No changes required to the `order` table structure

2. **Kafka Configuration**:

   - Setting up topics and consumer groups for refund processing

3. **External Service Connections**:

   - Updating Acquiring Core and Account Service adapters

4. **Temporal Workflow Configuration**:
   - Configuring workflows for refund settlement and processing

## Pre-Deployment Verification

### 1. Environment Requirements

- [ ] Go version 1.22.5 or later is installed
- [ ] gRPC-Go v1.64.0 or later is available
- [ ] Protocol Buffers v5.28.3 or later is installed

## Infrastructure Deployment Checklist

### 1. Docker Configuration

- [ ] Update Dockerfile ENTRYPOINT from `["/app", "start"]` to `["/app"]`
- [ ] Verify Docker image builds successfully with the new ENTRYPOINT
- [ ] Update Kubernetes deployment manifests if they reference the "start" command

### 2. Database Setup

#### Database Tables

- [ ] Create new tables for refund processing:

**refund_logs table**

```sql
CREATE TABLE IF NOT EXISTS `refund_logs` (
    `id`                        bigint auto_increment NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `refund_id`                 bigint             NOT NULL,
    `amount`                    bigint             NOT NULL,
    `app_trans_id`              varchar(255)       NOT NULL,
    `app_id`                    int                NOT NULL,
    `payment_description`       varchar(1024)      NOT NULL,
    `refund_type`               enum ('AUTO', 'MANUAL')    NOT NULL,
    `status`                    enum ('INIT', 'SUCCESS', 'FAILED', 'PENDING', 'PROCESSING') NOT NULL,
    `bank_status`               varchar(20)              NOT NULL DEFAULT '',
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `process_type`              enum ('SETTLEMENT', 'REPAYMENT', 'FUNDBACK', 'MANUAL') NULL,
    `deadline_at`               datetime           NULL,
    `extra`                     json               NOT NULL,
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zp_trans_id` (`zp_trans_id`),
    KEY `idx_deadline_process_type` (`deadline_at`, `process_type`)
)
```

**refund_settle table**

```sql
CREATE TABLE IF NOT EXISTS `refund_settle` (
    `id`                        bigint auto_increment NOT NULL,
    `status`                    enum ('INIT', 'PENDING', 'PROCESSING', 'SETTLED', 'COMPLETED') NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `net_refund_amount`         bigint             NOT NULL,
    `total_refund_amount`       bigint             NOT NULL,
    `settlement_amount`         bigint             NOT NULL,
    `user_topup_amount`         bigint             NOT NULL DEFAULT 0,
    `user_payback_amount`       bigint             NOT NULL DEFAULT 0,
    `event_version`             int                NOT NULL DEFAULT 0,
    `metadata`                  json               NOT NULL DEFAULT '{}',
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_settle` (`zp_trans_id`)
)
```

#### Update Existing Tables

**Update payment_logs table**

- [ ] Add new field to the `payment_logs` table:
  ```sql
  ALTER TABLE payment_logs ADD COLUMN order_id bigint AFTER id;
  ```
- [ ] Update the CreatePaymentLog SQL query to include the new fields:
  ```sql
  -- name: CreatePaymentLog :execresult
  INSERT INTO payment_logs(zp_trans_id,
                         order_id,
                         system_id,
                         trans_type,
                         partner_req_id,
                         partner_trans_id,
                         ...
  ```

**Update order table**

- [ ] No schema changes required for the `order` table as it already has the necessary fields:
  - `type` enum with 'refund' value
  - `refund_id` field for linking to refund records
  - `settle_id` field for settlement tracking

### 3. Kafka Configuration

#### Kafka Topics

- [ ] Create and configure the following Kafka topics:
  - `ZPReportTransLog` - For receiving refund information from Acquiring Core
  - `prod.fin.installment.payment.refund.settle_process` - For refund settlement processing

#### Consumer Groups

- [ ] Set up the following consumer groups:
  - `prod.fin.installment.payment.ac_refund.group` - For consuming refund information updates
  - `prod.fin.installment.payment.refund.settle_process.group` - For processing refund settlements
  - `prod.fin.installment.payment.refund.settle_request.group` - For handling refund settlement requests
  - `prod.fin.installment.payment.refund.settle_response.group` - For processing refund settlement responses

#### Kafka Configuration Settings

- [ ] Configure each consumer with 2 workers for parallel processing
- [ ] Set appropriate message retention periods (recommended: 7 days)
- [ ] Configure error handling and dead letter queues

### 4. External Service Adapters

#### Acquiring Core Service

- [ ] Update Acquiring Core adapter to support new features:
  - New OrderStatus values: `AUTHORIZED` and `CANCELED`
  - New PaymentStatus value: `PAYMENT_AUTHORIZED`
  - New CaptureMethod enum: `CAPTURE_METHOD_AUTO` and `CAPTURE_METHOD_MANUAL`
  - New message types: `CaptureRequest` and `CaptureResponse`
- [ ] Connection settings:
  - Host: `{{ fin.installment.adapters.acquiring_core_service.address }}`
  - Timeout: 30s
  - Secured: `{{ fin.installment.adapters.acquiring_core_service.secured }}`

#### Account Service

- [ ] Update Account Service adapter to use gRPC-Go v1.64.0 or later
- [ ] Update gRPC client configurations to use `grpc.StaticMethod()` option
- [ ] Connection settings:
  - Host: `{{ fin.installment.adapters.account_service.address }}`
  - Timeout: 30s
  - Secured: `{{ fin.installment.adapters.account_service.secured }}`

### 5. Configuration Files

- [ ] Update environment configuration in `config/prod.yaml`:

```yaml
payment:
  server:
    consumer:
      ac_refund_info_updated:
        brokers: { { fin.installment.payment.ac_kafka.refunds.brokers } }
        topic: "ZPReportTransLog"
        group_id: "prod.fin.installment.payment.ac_refund.group"
        num_workers: 2
      refund_settle_process:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_process.group"
        num_workers: 2
      refund_settle_request:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_request.group"
        num_workers: 2
      refund_settle_response:
        brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
        topic: "prod.fin.installment.payment.refund.settle_process"
        group_id: "prod.fin.installment.payment.refund.settle_response.group"
        num_workers: 2
  publishers:
    refund_settle_process:
      brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
      topic: "prod.fin.installment.payment.refund.settle_process"
  schedulers:
    refund_settle_recon:
      queue_name: "installment.refund"
      workflow_type: "ReconcileRefundSettle"
    refund_discharge_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingEarlyDischarge"
    refund_fundback_process:
      queue_name: "installment.refund"
      workflow_type: "FundbackAfterSettlement"
    refund_expired_process:
      queue_name: "installment.refund"
      workflow_type: "RefundExpiredProcess"
    refund_expired_repay_poll:
      queue_name: "installment.refund"
      workflow_type: "PollingExpiredRefundRepay"
  order_configs:
    refund_topup:
      - app_id: 4142
        mac_key: "{{ fin.installment.payment.order_configs.refund_topup.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_topup.callback_key }}"
        product_code: "TU010"
    refund_settle:
      - app_id: 4143
        mac_key: "{{ fin.installment.payment.order_configs.refund_settle.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_settle.callback_key }}"
        merchant_id: "{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}"
      - app_id: 4143
        mac_key: "{{ fin.installment.payment.order_configs.refund_settle.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_settle.callback_key }}"
        merchant_id: "{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}"
        partner_code: "CIMB"
    refund_fundback:
      - app_id: 4142
        mac_key: "{{ fin.installment.payment.order_configs.refund_fundback.mac_key }}"
        callback_key: "{{ fin.installment.payment.order_configs.refund_fundback.callback_key }}"
        product_code: "TF028"
```

### 6. Temporal Workflow Configuration

#### Temporal Connection Settings

- [ ] Configure Temporal connection:
  - Address: `{{ fin.installment.adapters.temporal.address }}`
  - Namespace: `{{ fin.installment.adapters.temporal.namespace }}`
  - Enable SSL: true

#### Refund Workflows

- [ ] Register and configure the following workflows:

  - `ReconcileRefundSettle` - Reconciles refund settlements
  - `PollingEarlyDischarge` - Polls for early discharge events
  - `FundbackAfterSettlement` - Processes fund returns after settlement
  - `RefundExpiredProcess` - Handles expired refunds
  - `PollingExpiredRefundRepay` - Polls for expired refund repayments

- [ ] Configure workflow queue:
  - Queue name: `installment.refund`

## Deployment Steps

### 1. Database Migration

- [ ] Apply the following database changes:

  - Create new `refund_logs` table for tracking refunds
  - Create new `refund_settle` table for managing refund settlements
  - Add `order_id` column to the `payment_logs` table
  - Update SQL queries to include new fields

- [ ] Run database migration scripts:

  ```bash
  # Create new refund tables
  mysql -h [HOST] -u [USERNAME] -p [DATABASE] < payment/resources/sql/refund.sql

  # Add order_id column to payment_logs table
  mysql -h [HOST] -u [USERNAME] -p [DATABASE] -e "ALTER TABLE payment_logs ADD COLUMN order_id bigint AFTER id;"
  ```

- [ ] Verify migrations completed successfully:

  ```bash
  # Check refund_logs table structure
  mysql -h [HOST] -u [USERNAME] -p [DATABASE] -e "DESCRIBE refund_logs;"

  # Check refund_settle table structure
  mysql -h [HOST] -u [USERNAME] -p [DATABASE] -e "DESCRIBE refund_settle;"

  # Verify payment_logs has order_id column
  mysql -h [HOST] -u [USERNAME] -p [DATABASE] -e "DESCRIBE payment_logs;"
  ```

### 2. Service Deployment

- [ ] Build the service with updated dependencies:
  ```bash
  go build -o app
  ```
- [ ] Update Docker image:
  ```bash
  docker build -t payment-service:latest .
  ```
- [ ] Deploy the service to the target environment:
  ```bash
  kubectl apply -f k8s/payment-service.yaml
  ```
- [ ] Verify service health checks are passing

### 3. Post-Deployment Verification

#### Kafka Verification

- [ ] Verify Kafka consumers are running and processing messages:
  ```bash
  # Check consumer group status
  kafka-consumer-groups.sh --bootstrap-server [BROKER] --describe --group prod.fin.installment.payment.ac_refund.group
  ```

#### Temporal Verification

- [ ] Verify Temporal workflows are registered and running:
  ```bash
  tctl --namespace [NAMESPACE] workflow list
  ```

#### End-to-End Testing

- [ ] Test the refund flow with the following steps:
  1. Create a test refund in a controlled environment
  2. Verify the refund is recorded in the `refund_logs` table
  3. Verify the settlement process is triggered
  4. Verify the refund is completed successfully

## Monitoring Setup

### 1. Metrics

- [ ] Configure metrics for refund processing:
  - [ ] Success rate
  - [ ] Processing time
  - [ ] Error rate
  - [ ] Queue depth

### 2. Logging

- [ ] Configure logging for refund-related events
- [ ] Set up log aggregation for refund processing

### 3. Alerting

- [ ] Configure alerts for refund processing issues
- [ ] Set up notification channels for alerts

## Rollback Plan

### 1. Service Rollback

- [ ] Revert to previous version of the service if issues are encountered
- [ ] Command: `kubectl rollout undo deployment/payment-service`

### 2. Database Rollback

- [ ] Restore database from backup if necessary
- [ ] Apply rollback migrations if available

### 3. Configuration Rollback

- [ ] Revert configuration changes if necessary

## Support Information

### 1. Contact Information

- **Primary Contact**: DevOps Team
- **Secondary Contact**: Payment Service Team
- **Slack Channel**: #fin-installment-support

### 2. Documentation

- Link to technical documentation
- Link to API documentation
- Link to troubleshooting guide
