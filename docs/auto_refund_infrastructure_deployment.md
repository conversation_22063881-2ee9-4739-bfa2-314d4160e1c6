# Auto Refund Feature - Infrastructure Deployment Guide

## 1. Database Setup

### 1.1 Database Tables

#### `refund_logs` Table
This table stores individual refund transactions.

```sql
CREATE TABLE `refund_logs` (
    `id`                        bigint auto_increment NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `refund_id`                 bigint             NOT NULL,
    `amount`                    bigint             NOT NULL,
    `app_trans_id`              varchar(255)       NOT NULL,
    `app_id`                    int                NOT NULL,
    `payment_description`       varchar(1024)      NOT NULL,
    `refund_type`               enum ('AUTO', 'MANUAL')    NOT NULL,
    `status`                    enum ('INIT', 'SUCCESS', 'FAILED', 'PENDING', 'PROCESSING') NOT NULL,
    `bank_status`               varchar(20)              NOT NULL DEFAULT '',
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `process_type`              enum ('SETTLEMENT', 'REPAYMENT', 'FUNDBACK', 'MANUAL') NULL,
    `deadline_at`               datetime           NULL,
    `extra`                     json               NOT NULL,
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zp_trans_id` (`zp_trans_id`),
    KEY `idx_deadline_process_type` (`deadline_at`, `process_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
```

**Key Fields:**
- `id`: Primary key
- `zp_trans_id`: ZaloPay transaction ID
- `refund_id`: Refund transaction ID
- `refund_type`: Type of refund (AUTO or MANUAL)
- `status`: Current status of the refund
- `process_type`: Type of refund process (SETTLEMENT, REPAYMENT, FUNDBACK, MANUAL)
- `deadline_at`: Deadline for processing the refund

**Indexes:**
- Primary Key: `id`
- `idx_zp_trans_id`: Index on `zp_trans_id` for quick lookups
- `idx_deadline_process_type`: Composite index on `deadline_at` and `process_type` for efficient querying of refunds by deadline and process type

#### `refund_settle` Table
This table manages the settlement process for refunds.

```sql
CREATE TABLE `refund_settle` (
    `id`                        bigint auto_increment NOT NULL,
    `status`                    enum ('INIT', 'PENDING', 'PROCESSING', 'SETTLED', 'COMPLETED') NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `net_refund_amount`         bigint             NOT NULL,
    `total_refund_amount`       bigint             NOT NULL,
    `settlement_amount`         bigint             NOT NULL,
    `user_topup_amount`         bigint             NOT NULL DEFAULT 0,
    `user_payback_amount`       bigint             NOT NULL DEFAULT 0,
    `event_version`             int                NOT NULL DEFAULT 0,
    `metadata`                  json               NOT NULL DEFAULT '{}',
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_settle` (`zp_trans_id`)
)ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
```

**Key Fields:**
- `id`: Primary key
- `status`: Current status of the settlement
- `zp_trans_id`: ZaloPay transaction ID
- `net_refund_amount`: Net amount to be refunded
- `total_refund_amount`: Total refund amount
- `settlement_amount`: Amount to be settled
- `user_topup_amount`: Amount to be topped up to the user
- `user_payback_amount`: Amount to be paid back by the user
- `event_version`: Version of the event for optimistic locking

**Indexes:**
- Primary Key: `id`
- `uk_refund_settle`: Unique key on `zp_trans_id` to ensure one settlement per transaction

### 1.2 Database Connection Configuration

The database connection is configured in the environment configuration file:

```yaml
data:
  database:
    driver: "mysql"
    host: {{ private-cicd.zalopay.mysql.1100090.host }}
    port: {{ private-cicd.zalopay.mysql.1100090.port }}
    username: {{ private-cicd.zalopay.mysql.1100090.user.262.username }}
    password: {{ private-cicd.zalopay.mysql.1100090.user.262.password }}
    db_name: "fin_installment_payment"
    max_open_conn: 32
    max_idle_conn: 8
    conn_max_life_time: 600s
    allow_native_passwords: true
```

## 2. Kafka Configuration

### 2.1 Kafka Topics and Consumer Groups

The following Kafka topics and consumer groups need to be configured:

#### Acquiring Core Refund Information Updates
```yaml
ac_refund_info_updated:
  brokers: {{ fin.installment.payment.ac_kafka.refunds.brokers }}
  topic: "ZPReportTransLog"
  group_id: "prod.fin.installment.payment.ac_refund.group"
  num_workers: 2
```

#### Refund Settlement Process
```yaml
refund_settle_process:
  brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
  topic: "prod.fin.installment.payment.refund.settle_process"
  group_id: "prod.fin.installment.payment.refund.settle_process.group"
  num_workers: 2
```

#### Refund Settlement Request
```yaml
refund_settle_request:
  brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
  topic: "prod.fin.installment.payment.refund.settle_process"
  group_id: "prod.fin.installment.payment.refund.settle_request.group"
  num_workers: 2
```

#### Refund Settlement Response
```yaml
refund_settle_response:
  brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
  topic: "prod.fin.installment.payment.refund.settle_process"
  group_id: "prod.fin.installment.payment.refund.settle_response.group"
  num_workers: 2
```

### 2.2 Kafka Publishers

```yaml
publishers:
  refund_settle_process:
    brokers: "{{ private-cicd.zalopay.kafka.kafka-cluster-1.host }}:{{ private-cicd.zalopay.kafka.kafka-cluster-1.port }}"
    topic: "prod.fin.installment.payment.refund.settle_process"
```

## 3. External Service Adapters

### 3.1 Acquiring Core Service

The Acquiring Core Service adapter needs to be configured to support the new order statuses and payment methods:

```yaml
acquiring_core_service:
  grpc:
    address: {{ fin.installment.adapters.acquiring_core_service.address }}
    timeout: 30s
    secured: {{ fin.installment.adapters.acquiring_core_service.secured }}
    client_id: {{ fin.installment.adapters.acquiring_core_service.client_id }}
    client_key: {{ fin.installment.adapters.acquiring_core_service.client_key }}
```

**Important Changes:**
- Support for new OrderStatus values: `AUTHORIZED` and `CANCELED`
- Support for new PaymentStatus value: `PAYMENT_AUTHORIZED`
- Support for new CaptureMethod enum: `CAPTURE_METHOD_AUTO` and `CAPTURE_METHOD_MANUAL`
- Support for new message types: `CaptureRequest` and `CaptureResponse`

### 3.2 Account Service

The Account Service adapter needs to be updated to use the new gRPC version:

```yaml
account_service:
  address: {{ fin.installment.adapters.account_service.address }}
  secured: {{ fin.installment.adapters.account_service.secured }}
  timeout: 30s
```

**Important Changes:**
- Update to gRPC-Go v1.64.0 or later
- Use of `grpc.StaticMethod()` option in gRPC client calls

## 4. Temporal Workflow Configuration

### 4.1 Temporal Connection

```yaml
temporal:
  address: {{ fin.installment.adapters.temporal.address }}
  namespace: {{ fin.installment.adapters.temporal.namespace }}
  enable_ssl: true
```

### 4.2 Refund Workflows

The following Temporal workflows need to be configured:

```yaml
schedulers:
  refund_settle_recon:
    queue_name: "installment.refund"
    workflow_type: "ReconcileRefundSettle"
  refund_discharge_poll:
    queue_name: "installment.refund"
    workflow_type: "PollingEarlyDischarge"
  refund_fundback_process:
    queue_name: "installment.refund"
    workflow_type: "FundbackAfterSettlement"
  refund_expired_process:
    queue_name: "installment.refund"
    workflow_type: "RefundExpiredProcess"
  refund_expired_repay_poll:
    queue_name: "installment.refund"
    workflow_type: "PollingExpiredRefundRepay"
```

## 5. Order Configurations

The following order configurations need to be set up for refund processing:

### 5.1 Refund Topup Configuration

```yaml
refund_topup:
  - app_id: 4142
    mac_key: "{{ fin.installment.payment.order_configs.refund_topup.mac_key }}"
    callback_key: "{{ fin.installment.payment.order_configs.refund_topup.callback_key }}"
    product_code: "TU010"
```

### 5.2 Refund Settlement Configuration

```yaml
refund_settle:
  - app_id: 4143
    mac_key: "{{ fin.installment.payment.order_configs.refund_settle.mac_key }}"
    callback_key: "{{ fin.installment.payment.order_configs.refund_settle.callback_key }}"
    merchant_id: "{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}"
  - app_id: 4143
    mac_key: "{{ fin.installment.payment.order_configs.refund_settle.mac_key }}"
    callback_key: "{{ fin.installment.payment.order_configs.refund_settle.callback_key }}"
    merchant_id: "{{ fin.installment.payment.order_configs.refund_settle.merchant_id }}"
    partner_code: "CIMB"
```

### 5.3 Refund Fundback Configuration

```yaml
refund_fundback:
  - app_id: 4142
    mac_key: "{{ fin.installment.payment.order_configs.refund_fundback.mac_key }}"
    callback_key: "{{ fin.installment.payment.order_configs.refund_fundback.callback_key }}"
    product_code: "TF028"
```

## 6. Deployment Steps

### 6.1 Database Migration

1. Execute the SQL scripts to create the `refund_logs` table if it doesn't exist
2. Execute the SQL scripts to create the `refund_settle` table if it doesn't exist
3. Verify that the tables have been created successfully

### 6.2 Kafka Setup

1. Create the Kafka topics if they don't exist:
   - `ZPReportTransLog`
   - `prod.fin.installment.payment.refund.settle_process`
2. Configure the consumer groups:
   - `prod.fin.installment.payment.ac_refund.group`
   - `prod.fin.installment.payment.refund.settle_process.group`
   - `prod.fin.installment.payment.refund.settle_request.group`
   - `prod.fin.installment.payment.refund.settle_response.group`

### 6.3 External Service Configuration

1. Update the Acquiring Core Service adapter to support the new order statuses and payment methods
2. Update the Account Service adapter to use the new gRPC version
3. Verify connectivity to the external services

### 6.4 Temporal Workflow Registration

1. Register the Temporal workflows:
   - `ReconcileRefundSettle`
   - `PollingEarlyDischarge`
   - `FundbackAfterSettlement`
   - `RefundExpiredProcess`
   - `PollingExpiredRefundRepay`
2. Verify that the workflows are registered successfully

### 6.5 Order Configuration Setup

1. Configure the refund topup settings
2. Configure the refund settlement settings
3. Configure the refund fundback settings
4. Verify that the configurations are applied correctly

## 7. Verification Steps

### 7.1 Database Verification

1. Verify that the `refund_logs` table exists and has the correct schema
2. Verify that the `refund_settle` table exists and has the correct schema
3. Verify that the database connection is working correctly

### 7.2 Kafka Verification

1. Verify that the Kafka topics exist
2. Verify that the consumer groups are registered
3. Verify that the consumers are processing messages correctly

### 7.3 External Service Verification

1. Verify connectivity to the Acquiring Core Service
2. Verify connectivity to the Account Service
3. Verify that the gRPC calls are working correctly

### 7.4 Temporal Workflow Verification

1. Verify that the workflows are registered
2. Verify that the workflows can be started
3. Verify that the workflows are executing correctly

### 7.5 End-to-End Verification

1. Create a test refund
2. Verify that the refund is processed correctly
3. Verify that the refund settlement is processed correctly
4. Verify that the refund is completed successfully
