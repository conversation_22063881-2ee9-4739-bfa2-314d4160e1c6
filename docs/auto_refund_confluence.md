# Auto Refund Feature Deployment

## Overview

The Auto Refund feature enables automatic processing of refunds in the payment service. This document provides comprehensive information for deploying and maintaining this feature in production.

## Feature Description

The Auto Refund feature allows the system to:

- Process refunds automatically without manual intervention
- Track refund status through the entire lifecycle
- Settle refunds with appropriate accounting entries
- Handle early discharge scenarios
- Process expired refunds

## Architecture

![Auto Refund Architecture](https://via.placeholder.com/800x400?text=Auto+Refund+Architecture)

The Auto Refund feature is implemented as part of the payment service and interacts with several other components:

- **Acquiring Core Service**: Provides payment transaction information and processes refund requests
- **Account Service**: Manages user account information and handles fund transfers
- **Installment Service**: Provides installment plan information and receives updates about refunds

## Deployment Requirements

### Infrastructure Requirements

| Component | Requirement                     |
| --------- | ------------------------------- |
| CPU       | 2 cores per instance            |
| Memory    | 4GB per instance                |
| Storage   | 20GB per instance               |
| Instances | Minimum 2 for high availability |

### Software Requirements

| Software         | Version          |
| ---------------- | ---------------- |
| Go               | 1.22.5 or later  |
| gRPC             | v1.64.0 or later |
| Protocol Buffers | v5.28.3 or later |
| MySQL            | 8.0 or later     |
| Kafka            | 2.8 or later     |

### Network Requirements

| Connection   | Port | Protocol |
| ------------ | ---- | -------- |
| gRPC Service | 9090 | TCP      |
| HTTP Service | 8080 | TCP      |
| MySQL        | 3306 | TCP      |
| Kafka        | 9092 | TCP      |

## Deployment Steps

### 1. Pre-deployment Checklist

- [ ] Database backup completed
- [ ] Configuration values validated
- [ ] Kafka topics created
- [ ] Monitoring and alerting configured
- [ ] Rollback plan reviewed

### 2. Database Migration

Execute the SQL scripts to create or update the necessary database tables:

```sql
-- Create new refund tables from payment/resources/sql/refund.sql
CREATE TABLE IF NOT EXISTS `refund_logs` (
    `id`                        bigint auto_increment NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `refund_id`                 bigint             NOT NULL,
    `amount`                    bigint             NOT NULL,
    `app_trans_id`              varchar(255)       NOT NULL,
    `app_id`                    int                NOT NULL,
    `payment_description`       varchar(1024)      NOT NULL,
    `refund_type`               enum ('AUTO', 'MANUAL')    NOT NULL,
    `status`                    enum ('INIT', 'SUCCESS', 'FAILED', 'PENDING', 'PROCESSING') NOT NULL,
    `bank_status`               varchar(20)              NOT NULL DEFAULT '',
    `error_code`                varchar(255)             NOT NULL DEFAULT '',
    `error_message`             varchar(255)             NOT NULL DEFAULT '',
    `process_type`              enum ('SETTLEMENT', 'REPAYMENT', 'FUNDBACK', 'MANUAL') NULL,
    `deadline_at`               datetime           NULL,
    `extra`                     json               NOT NULL,
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_zp_trans_id` (`zp_trans_id`),
    KEY `idx_deadline_process_type` (`deadline_at`, `process_type`)
);

CREATE TABLE IF NOT EXISTS `refund_settle` (
    `id`                        bigint auto_increment NOT NULL,
    `status`                    enum ('INIT', 'PENDING', 'PROCESSING', 'SETTLED', 'COMPLETED') NOT NULL,
    `zp_trans_id`               bigint             NOT NULL,
    `net_refund_amount`         bigint             NOT NULL,
    `total_refund_amount`       bigint             NOT NULL,
    `settlement_amount`         bigint             NOT NULL,
    `user_topup_amount`         bigint             NOT NULL DEFAULT 0,
    `user_payback_amount`       bigint             NOT NULL DEFAULT 0,
    `event_version`             int                NOT NULL DEFAULT 0,
    `metadata`                  json               NOT NULL DEFAULT '{}',
    `created_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                datetime           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_settle` (`zp_trans_id`)
);

-- Add order_id column to payment_logs table
ALTER TABLE payment_logs ADD COLUMN order_id bigint AFTER id;
```

### 3. Configuration Updates

Update the configuration files with the necessary settings for the Auto Refund feature:

```yaml
payment:
  server:
    consumer:
      ac_refund_info_updated:
        # Configuration details as in the deployment guide
      refund_settle_process:
        # Configuration details as in the deployment guide
  # Additional configuration sections
```

### 4. Service Deployment

1. Build the service:

   ```bash
   go build -o app
   ```

2. Update the Docker image:

   ```bash
   docker build -t payment-service:latest .
   ```

3. Deploy the service using Kubernetes:
   ```bash
   kubectl apply -f k8s/payment-service.yaml
   ```

### 5. Post-deployment Verification

- [ ] Service health checks passing
- [ ] API endpoints accessible
- [ ] Kafka consumers processing messages
- [ ] Database connections established
- [ ] Test refund flow completed successfully

## Monitoring

### Key Metrics

| Metric                 | Description                            | Alert Threshold |
| ---------------------- | -------------------------------------- | --------------- |
| refund_processing_rate | Number of refunds processed per minute | < 10 per minute |
| refund_success_rate    | Percentage of successful refunds       | < 95%           |
| refund_processing_time | Time taken to process refunds          | > 5 seconds     |
| refund_error_rate      | Number of errors encountered           | > 5% of total   |
| refund_queue_depth     | Number of pending refund requests      | > 1000          |

### Logging

Important log events to monitor:

- Refund initiation
- Status transitions
- Settlement processing
- Error conditions

## Troubleshooting

### Common Issues

| Issue                                | Possible Cause                         | Resolution                        |
| ------------------------------------ | -------------------------------------- | --------------------------------- |
| Refunds stuck in PENDING state       | Kafka consumer not processing messages | Restart Kafka consumer            |
| High error rate in refund processing | Integration issue with Acquiring Core  | Check Acquiring Core connectivity |
| Database connection errors           | Connection pool exhaustion             | Increase connection pool size     |
| Slow refund processing               | High system load                       | Scale up service instances        |

### Support Contacts

| Role              | Contact              | Availability   |
| ----------------- | -------------------- | -------------- |
| Primary Support   | DevOps Team          | 24/7           |
| Secondary Support | Payment Service Team | Business hours |
| Escalation        | Platform Engineering | On-call        |

## Rollback Procedure

If issues are encountered during or after deployment, follow these steps:

1. Assess the impact and determine if rollback is necessary
2. If rollback is required:
   - Revert to the previous version of the service
   - Restore database if necessary
   - Verify system functionality after rollback
3. Document the issues encountered and the rollback process

## References

- [Auto Refund Technical Documentation](auto_refund_technical_details.md)
- [Auto Refund Commit Changes Analysis](auto_refund_commit_changes.md)
- [Payment Service Architecture](https://confluence.example.com/payment-service-architecture)
- [Refund Process Flow](https://confluence.example.com/refund-process-flow)

## Approval

| Role           | Name | Approval Date |
| -------------- | ---- | ------------- |
| Product Owner  |      |               |
| Technical Lead |      |               |
| DevOps Lead    |      |               |
| Security Lead  |      |               |
