syntax = "proto3";

package api.payment.v1;
option go_package = "gitlab.zalopay.vn/fin/installment/payment-service/api/payment/v1;v1";

import "validate/validate.proto";
import "payment/v1/common.proto";
import "google/api/annotations.proto";

service RefundService {
  rpc PreCheck (PreCheckRequest) returns (PreCheckResponse) {}
  rpc Refund (RefundRequest) returns (RefundResponse) {}
  rpc RefundQuery (RefundQueryRequest) returns (RefundQueryResponse) {}
  rpc CreateTopup (CreateTopupRequest) returns (CreateTopupResponse) {
    option (google.api.http) = {
      post: "/payment/v1/refund/topups"
      body: "*"
    };
  }
  rpc CalculateInterestEstimation(CalculateInterestEstimationRequest) returns (CalculateInterestEstimationResponse) {}
}

service RefundOpsService {
  rpc RetryRefundSettleOrder(RetryRefundSettleOrderRequest) returns (RetryRefundSettleOrderResponse) {}
  rpc TriggerProcessSettleInfo(TriggerProcessSettleInfoRequest) returns (TriggerProcessSettleInfoResponse) {}
  rpc TriggerReconcileSettleJobs(TriggerReconcileSettleJobsRequest) returns (TriggerReconcileSettleJobsResponse) {}
  rpc TriggerPollingEarlyDischargeJobs(TriggerPollingEarlyDischargeJobsRequest) returns (TriggerPollingEarlyDischargeJobsResponse) {}
}

message PreCheckRequest {
  int64 timestamp = 1;
  string zp_trans_id = 2 [(validate.rules).string.min_len = 1];
  int64 amount = 3 [(validate.rules).int64.gte = 1];
  string app_trans_id = 4 [(validate.rules).string.min_len = 1];
  int32 app_id = 5 [(validate.rules).int32.gte = 1];
  int64 refund_id = 6 [(validate.rules).int64.gte = 1];
  string payment_description = 7;
}

message PreCheckResponse {
  int64 refund_id = 1;
  int64 ref_sof_id = 2;
  RefundType refund_type = 3;
  SettleStatus settle_status = 4;
}

message RefundRequest {
  int64 timestamp = 1;
  string zp_trans_id = 2 [(validate.rules).string.min_len = 1];
  int64 amount = 3 [(validate.rules).int64.gte = 1];
  string app_trans_id = 4 [(validate.rules).string.min_len = 1];
  int32 app_id = 5 [(validate.rules).int32.gte = 1];
  int64 refund_id = 6 [(validate.rules).int64.gte = 1];
  string payment_description = 7;
}

message RefundResponse {
  int64 timestamp = 1;
  int64 refund_id = 2;
  int64 ref_sof_id = 3;
  RefundType refund_type = 4;
  TransStatus trans_status = 5;
  ErrorDetail error_detail = 6;
}

message RefundQueryRequest {
  int64 timestamp = 1;
  int64 refund_id = 2 [(validate.rules).int64.gte = 1];
}

message RefundQueryResponse {
  string escrow_bank_account = 1;
  TransStatus trans_status = 2;
  int64 ref_sof_id = 3;
  int64 refund_id = 4;
}

message CreateTopupRequest {
  int64 amount = 1 [json_name = "amount", (validate.rules).int64.gte = 1000];
  int64 zp_trans_id = 2 [json_name = "zp_trans_id", (validate.rules).int64.gte = 1];
}

message CreateTopupResponse {
  int64 trans_id = 1 [json_name = "trans_id"];
  int32 app_id = 2 [json_name = "app_id"];
  string app_trans_id = 3 [json_name = "app_trans_id"];
  string zp_trans_token = 4 [json_name = "zp_trans_token"];
}

message CalculateInterestEstimationRequest {
  string zp_trans_id = 1 [(validate.rules).string.min_len = 1];
  string estimation_date = 2 [(validate.rules).string.min_len = 1];
}

message CalculateInterestEstimationResponse {
  int64 total_interest = 1;
}

message TriggerReconcileSettleJobsRequest {
  repeated int64 zp_trans_ids = 1;
}

message TriggerReconcileSettleJobsResponse {
  repeated int64 zp_trans_ids = 1;
}

message TriggerProcessSettleInfoRequest {
  repeated int64 zp_trans_ids = 1;
}

message TriggerProcessSettleInfoResponse {
  repeated int64 zp_trans_ids = 1;
  int32 processed_count = 2;
}

message TriggerPollingEarlyDischargeJobsRequest {
  // Optional list of specific transaction IDs to poll
  repeated int64 payment_ids = 1;
}

message TriggerPollingEarlyDischargeJobsResponse {
  // List of transaction IDs that were successfully processed
  repeated int64 payment_ids = 1;
  
  // Total count of processed transactions
  int32 processed_count = 2;
  
  // Any errors encountered during processing
  repeated string error_messages = 3;
}

message RetryRefundSettleOrderRequest {
  repeated int64 zp_trans_ids = 1;
}

message RetryRefundSettleOrderResponse {
  repeated int64 zp_trans_ids = 1;
  int32 processed_count = 2;
}

message RefundSettleEvent {
  message RefundSettleResult {
    int64 trans_id = 1;
    int64 order_id = 2;
    int32 app_id = 3;
    string app_trans_id = 4;
    TransStatus trans_status = 5;
    string error_message = 6;
  }

  string event_id = 1;
  RefundSettleEventType event_type = 2;
  SettleStatus settle_status = 3;
  int64 ref_zp_trans_id = 4;
  int64 settle_amount = 5;

  // The context of the settlement
  oneof settle_context {
    RefundStandardSettle standard_settle = 6;
    RefundExpiredSettle expired_settle = 7;
  }

  RefundSettleResult settle_result = 50; 
}

// For normal settlement processing
message RefundStandardSettle {
  int64 settle_id = 1;
}

// For expired refund log settlement
message RefundExpiredSettle {
  int64 refund_id = 1;  // The specific log being processed
}

enum RefundSettleEventType {
  REFUND_SETTLE_EVENT_UNKNOWN = 0;
  REFUND_SETTLE_EVENT_REQUEST = 1;
  REFUND_SETTLE_EVENT_RESPONSE = 2;
}

enum RefundType {
  REFUND_TYPE_UNKNOWN = 0;
  REFUND_TYPE_AUTO = 1;
  REFUND_TYPE_MANUAL = 2;
}
