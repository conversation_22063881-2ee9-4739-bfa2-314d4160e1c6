package refund

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/adapters"
	repository_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/repository"
	"go.uber.org/mock/gomock"
)

type RefundTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	usecase *Usecase

	// Repository mocks
	mockRefundRepo  *repository_mocks.MockRefundRepo
	mockPaymentRepo *repository_mocks.MockPaymentRepo
	mockOrderRepo   *repository_mocks.MockOrderRepo
	mockTransaction *repository_mocks.MockTransaction

	// Adapter mocks
	mockAccountAdapter       *adapter_mocks.MockAccountAdapter
	mockPartnerConnector     *adapter_mocks.MockPartnerConnector
	mockAcquiringCore        *adapter_mocks.MockAcquiringCoreAdapter
	mockInstallmentAdapter   *adapter_mocks.MockInstallmentAdapter
	mockTaskJobAdapter       *adapter_mocks.MockTaskJobAdapter
	mockDistributedLock      *adapter_mocks.MockDistributedLock
	mockRefundSettleNotifier *adapter_mocks.MockRefundSettleNotifier

	// Test configs
	paymentConfig      *configs.Payment
	orderConfigsHelper *configs.OrderConfigsHelper
	logger             log.Logger
}

func (suite *RefundTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())

	// Initialize repository mocks
	suite.mockRefundRepo = repository_mocks.NewMockRefundRepo(suite.ctrl)
	suite.mockPaymentRepo = repository_mocks.NewMockPaymentRepo(suite.ctrl)
	suite.mockOrderRepo = repository_mocks.NewMockOrderRepo(suite.ctrl)
	suite.mockTransaction = repository_mocks.NewMockTransaction(suite.ctrl)

	// Initialize adapter mocks
	suite.mockAccountAdapter = adapter_mocks.NewMockAccountAdapter(suite.ctrl)
	suite.mockPartnerConnector = adapter_mocks.NewMockPartnerConnector(suite.ctrl)
	suite.mockAcquiringCore = adapter_mocks.NewMockAcquiringCoreAdapter(suite.ctrl)
	suite.mockInstallmentAdapter = adapter_mocks.NewMockInstallmentAdapter(suite.ctrl)
	suite.mockTaskJobAdapter = adapter_mocks.NewMockTaskJobAdapter(suite.ctrl)
	suite.mockDistributedLock = adapter_mocks.NewMockDistributedLock(suite.ctrl)
	suite.mockRefundSettleNotifier = adapter_mocks.NewMockRefundSettleNotifier(suite.ctrl)

	// Initialize configs
	suite.logger = log.DefaultLogger
	suite.paymentConfig = &configs.Payment{
		Refund: &configs.Refund{},
	}
	suite.orderConfigsHelper = &configs.OrderConfigsHelper{}

	// Initialize usecase with all dependencies
	suite.usecase = NewRefundUsecase(
		suite.logger,
		suite.mockRefundRepo,
		suite.mockTransaction,
		suite.mockTaskJobAdapter,
		suite.mockDistributedLock,
		suite.paymentConfig,
		suite.orderConfigsHelper,
		suite.mockOrderRepo,
		suite.mockPaymentRepo,
		suite.mockPartnerConnector,
		suite.mockAcquiringCore,
		suite.mockAccountAdapter,
		suite.mockRefundSettleNotifier,
		suite.mockInstallmentAdapter,
	)
}

func (suite *RefundTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

// Test basic usecase initialization
func (suite *RefundTestSuite) TestUsecaseInitialization() {
	// Test that the usecase is properly initialized with all dependencies
	suite.NotNil(suite.usecase)
	suite.NotNil(suite.mockRefundRepo)
	suite.NotNil(suite.mockPaymentRepo)
	suite.NotNil(suite.mockOrderRepo)
	suite.NotNil(suite.mockTransaction)
	suite.NotNil(suite.mockAccountAdapter)
	suite.NotNil(suite.mockPartnerConnector)
	suite.NotNil(suite.mockAcquiringCore)
	suite.NotNil(suite.mockInstallmentAdapter)
	suite.NotNil(suite.mockTaskJobAdapter)
	suite.NotNil(suite.mockDistributedLock)
	suite.NotNil(suite.mockRefundSettleNotifier)
}

func TestRefundTestSuite(t *testing.T) {
	suite.Run(t, new(RefundTestSuite))
}
