package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.uber.org/mock/gomock"
)

// Setup configuration helper
func (suite *RefundTestSuite) setupConfig() {
	orderConfigs := &configs.OrderConfigs{
		RefundSettle: []*configs.OrderConfig{{AppId: 4143, PartnerCode: nil}},
	}
	suite.orderConfigsHelper = configs.NewOrderConfigs(orderConfigs)
	suite.usecase = NewRefundUsecase(
		suite.logger, suite.mockRefundRepo, suite.mockTransaction, suite.mockTaskJobAdapter,
		suite.mockDistributedLock, suite.paymentConfig, suite.orderConfigsHelper,
		suite.mockOrderRepo, suite.mockPaymentRepo, suite.mockPartnerConnector,
		suite.mockAcquiringCore, suite.mockAccountAdapter, suite.mockRefundSettleNotifier,
		suite.mockInstallmentAdapter,
	)
}

// Mock data helpers
func mockExpiredRequest() *ExpiredRefundSettleRequest {
	return &ExpiredRefundSettleRequest{RefundID: 123, SettleAmount: 100000, RefZPTransID: 456}
}

func mockPurchaseOrder() *model.PurchaseOrder {
	return &model.PurchaseOrder{
		AccountInfo: model.Account{ZalopayID: 789, PartnerCode: "CIMB", PartnerAccountId: "acc123"},
	}
}

func mockRepaymentLog() *model.RepaymentLog {
	return &model.RepaymentLog{
		ID: 1, Order: model.Order{ID: 1, Amount: 100000, AppTransID: "test_app_trans_id", AppID: 4143},
		AccountInfo: model.Account{ZalopayID: 789, PartnerAccountId: "acc123"},
		BankRoute:   model.BankRoute{BankAccountNumber: "corp123"},
	}
}

// Main test for ExcuteExpiredRefundSettlement - Success case
func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_Success() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	// Main flow
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockOrderRepo.EXPECT().CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockPaymentRepo.EXPECT().CreateRepaymentLog(gomock.Any(), gomock.Any()).Return(mockRepaymentLog(), nil)
	suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(nil)

	// Process repayment
	suite.mockPartnerConnector.EXPECT().ODRepayment(ctx, gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusComplete}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(ctx, gomock.Any()).Return(nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(ctx, "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(ctx, gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.NoError(suite.T(), err)
}

// Test not eligible case
func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_NotEligible() {
	ctx := context.Background()
	params := mockExpiredRequest()

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeSettlement}, nil)

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.NoError(suite.T(), err) // Not eligible is not an error
}

// Test ProcessRepayment - Success case
func (suite *RefundTestSuite) TestProcessRepayment_Success() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusComplete}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(gomock.Any(), "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err)
}

// Test ProcessRepayment - Pending status
func (suite *RefundTestSuite) TestProcessRepayment_PendingStatus() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusProcessing}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err)
}

// Test ProcessExpiredRefund - Success case
func (suite *RefundTestSuite) TestProcessExpiredRefund_Success() {
	ctx := context.Background()
	mockSettleIDs := []int64{1, 2}

	// Mock channel building
	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return(mockSettleIDs, nil).Times(1)
	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return([]int64{}, nil).Times(1)

	// Mock worker processing
	suite.mockTaskJobAdapter.EXPECT().ExecuteReconcileRefundSettleJob(gomock.Any(), gomock.Any()).
		Return(nil).AnyTimes()

	for _, settleID := range mockSettleIDs {
		suite.mockRefundRepo.EXPECT().GetRefundSettleByID(gomock.Any(), settleID).
			Return(&model.RefundSettle{ID: settleID, ZPTransID: settleID + 100}, nil)

		suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
		suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
		suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(gomock.Any(), settleID+100).
			Return([]*model.RefundOrder{{ID: settleID, ZPTransID: settleID + 100}}, nil)
		suite.mockRefundRepo.EXPECT().MarkRefundLogsAsExpiredByIDs(gomock.Any(), []int64{settleID}).Return(nil)
		suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredEvents(gomock.Any(), settleID+100, gomock.Any()).Return(nil)
		suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(nil)
	}

	err := suite.usecase.ProcessExpiredRefund(ctx)
	assert.NoError(suite.T(), err)
}

// Test ProcessExpiredRefund - Nothing to process
func (suite *RefundTestSuite) TestProcessExpiredRefund_NothingToProcess() {
	ctx := context.Background()

	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return([]int64{}, nil)

	err := suite.usecase.ProcessExpiredRefund(ctx)
	assert.NoError(suite.T(), err)
}

// Test Refund function - Success case
func (suite *RefundTestSuite) TestRefund_Success() {
	ctx := context.Background()
	req := model.RefundOrder{ZPTransID: 123, Amount: 100000}

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(gomock.Any(), int64(123)).
		Return(&model.PurchaseOrder{Status: model.PaymentStatusSucceeded}, nil)
	suite.mockRefundRepo.EXPECT().CreateRefundLog(gomock.Any(), gomock.Any()).
		Return(&model.RefundOrder{ID: 1, Status: model.RefundStatusSuccess}, nil)

	result, err := suite.usecase.Refund(ctx, req)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
}

// Test Refund function - Payment not succeeded
func (suite *RefundTestSuite) TestRefund_PaymentNotSucceeded() {
	ctx := context.Background()
	req := model.RefundOrder{ZPTransID: 123, Amount: 100000}

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(gomock.Any(), int64(123)).
		Return(&model.PurchaseOrder{Status: model.PaymentStatusFailed}, nil)

	result, err := suite.usecase.Refund(ctx, req)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "payment status is not success")
}

// Test RefundQuery function - Success case
func (suite *RefundTestSuite) TestRefundQuery_Success() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockRefundRepo.EXPECT().GetRefundLogByRefundID(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: 123, Status: model.RefundStatusSuccess}, nil)

	result, err := suite.usecase.RefundQuery(ctx, refundID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
}

// Test RefundQuery function - Refund not found
func (suite *RefundTestSuite) TestRefundQuery_NotFound() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockRefundRepo.EXPECT().GetRefundLogByRefundID(gomock.Any(), refundID).
		Return(nil, errors.New("refund not found"))

	result, err := suite.usecase.RefundQuery(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "refund not found")
}
